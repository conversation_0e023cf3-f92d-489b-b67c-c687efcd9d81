var moment          = require('moment'),
    commonService   = require('../services/commonService'),
    securityService = require('../services/securityService');

    let hepatitis = {
        index: function(req, res){
            try {
                 const arrPromise = [];
                const errors = [];
                const patient_id = req.params.patient_id;
                const user = req.user;
                let patient = {};
                let detailHepatitis = {};
                let times = [];
                let timeActiveId;
                let typeDetail = {view: 'index'};
                const type = req.params.type;
                if(user.isAdmin || user.role_id.includes(6)){
                    // lấy thông tin cơ bản
                    arrPromise.push(
                        commonService.getAllDataTable('patients', securityService.applyRoleBasedFiltering(req.user, {id: patient_id})).then(responseData =>{
                            if(responseData.success){
                                if(responseData.data && responseData.data.length > 0){
                                    patient = responseData.data[0];
                                }
                            }else{
                                errors.push(responseData.message);  
                            }
                        })
                    )
                    typeDetail = hepatitis.getTypeDetail(type);
                    // L<PERSON>y thông tin viêm gan
                    arrPromise.push(
                        hepatitis.getDataHepatitis(patient_id, typeDetail.isTime, typeDetail.table, req.user).then(responseData =>{
                            detailHepatitis = responseData.detailHepatitis;
                            times = responseData.listTime;
                            timeActiveId = responseData.timeActiveId;
                        })
                    );
                }else{
                    errors.push('Bạn không có quyền truy cập bệnh nhân này!');
                }
        
                Promise.all(arrPromise).then(responseData =>{
                    return res.render('viem-gan-mt1/' + typeDetail.view, {
                        user: req.user,
                        errors: errors,
                        patient: patient,
                        moment: moment,
                        detailHepatitis: detailHepatitis,
                        times: times,
                        type: type,
                        timeActiveId: timeActiveId,
                        path: 'viem-gan-mt1'
                    });
                })
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack)
                return res.render("error");
            }
        },
        getDataHepatitis: function(patient_id, isTime, table, user){
            return new Promise(async (resolve, reject) => { 
                try {
                    let detailHepatitis = {};
                    let listTime = [];
                    let condition =  securityService.applyRoleBasedFiltering(user, {patient_id: patient_id, active: 1});
                    let timeActiveId;
                    if(isTime){
                        // Get list time
                        switch(table){
                            case 'viem_gan_mt1_sga': condition['type'] = 'sga';
                                break;
                            default: break;
                        }
                        let listTimeResult = await commonService.getAllDataTable('times', condition);
                        if(listTimeResult.success && listTimeResult.data && listTimeResult.data.length > 0){
                            listTime = listTimeResult.data;
                        }
                    }
                    
                    if(isTime && listTime.length > 0){
                        timeActiveId = listTime[0].id;
                        condition['time_id'] = timeActiveId;
                    }
                    if(condition.hasOwnProperty('type')) delete condition.type;
                    // Get detail hepastitis
                    commonService.getAllDataTable(table, condition).then(responseData =>{
                        if(responseData.success){
                            if(responseData.data && responseData.data.length > 0) detailHepatitis = responseData.data[0];
                        }
                        resolve({detailHepatitis: detailHepatitis, listTime: listTime, timeActiveId: timeActiveId});
                    })
                } catch (error) {
                    commonService.saveLog(req, error.message, error.stack);
                    resolve({detailHepatitis: {}, listTime: [], timeActiveId: null})
                }
            })
        },
        getTypeDetail: function(type){
            let view = 'index';
            let table = '';
            let isTime = false;
            switch(type){
                case 'dau-hieu-nhap-vien':
                    view = 'index';
                    table = 'viem_gan_mt1_dhnv';
                    break;  
                case 'sga':
                    view = 'sga';
                    table = 'viem_gan_mt1_sga';
                    isTime = true;
                    break;
                default: break;
            }
            return {view: view, table: table, isTime: isTime};
        },
        deleteTime: async function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: ""
                };
                let id = req.params.id;
                let patient_id = req.body.patient_id;
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultData.message = 'Bạn không có quyền xóa danh sách này!';
                    return res.json(resultData);
                }

                // Kiểm tra quyền sở hữu dữ liệu (user thường chỉ xóa được dữ liệu do họ tạo)
                // if(!req.user.isAdmin && id){
                //     const checkOwnership = await commonService.getAllDataTable('times', {id: id, patient_id: patient_id});
                //     if(checkOwnership.success && checkOwnership.data && checkOwnership.data.length > 0){
                //         const record = checkOwnership.data[0];
                //         if(record.created_by && record.created_by !== req.user.id){
                //             resultData.message = 'Bạn không có quyền xóa dữ liệu này!';
                //             return res.json(resultData);
                //         }
                //     }else{
                //         resultData.message = 'Không tìm thấy dữ liệu để xóa!';
                //         return res.json(resultData);
                //     }
                // }

                if(id){
                    commonService.updateRecordTable({active: 0},  securityService.applyRoleBasedFiltering(req.user, {id: id, patient_id: patient_id}), 'times').then(responseData =>{
                        if(responseData.success){
                            resultData.success = true;
                            resultData.message = 'Thành công!';
                            switch(req.params.type){
                                case 'sga':
                                    commonService.updateRecordTable({active: 0}, securityService.applyRoleBasedFiltering(req.user, {time_id: id, patient_id: patient_id}), 'viem_gan_mt1_sga')
                                    break;
                                default: break;
                            }
                        }else{
                            resultData.message = responseData.message;
                        }
                        return res.json(resultData);
                    })
                }else{
                    resultData.message = 'Thiếu Id bệnh nhân!';
                    return res.json(resultData);
                }
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
        editHepatitis: async function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: "",
                    data: ''
                };
                // new Date().toLocaleDateString('fr-CA');
                const validateRules = [
                //     { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
                //     { field: "ma_benh_an", type: "string", required: true, message: "Vui lòng nhập mã bệnh án!" }
                ];
                const parameter = hepatitis.getDataBodyHepatitis(req.body, req.params.type);
                const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultData.message = 'Bạn không có quyền sửa danh sách này!';
                    return res.json(resultData);
                }

                // Kiểm tra quyền sở hữu dữ liệu (user thường chỉ sửa được dữ liệu do họ tạo)
                // if(!req.user.isAdmin && parameter.condition.id){
                //     const checkOwnership = await commonService.getAllDataTable(parameter.table, {id: parameter.condition.id});
                //     if(checkOwnership.success && checkOwnership.data && checkOwnership.data.length > 0){
                //         const record = checkOwnership.data[0];
                //         if(record.created_by && record.created_by !== req.user.id){
                //             resultData.message = 'Bạn không có quyền sửa dữ liệu này!';
                //             return res.json(resultData);
                //         }
                //     }else{
                //         resultData.message = 'Không tìm thấy dữ liệu để sửa!';
                //         return res.json(resultData);
                //     }
                // }

                if(errors.length > 0){
                    resultData.message = errors.map(s => s.message).join(', ');
                    return res.json(resultData);
                }else{
                    commonService.updateRecordTable(parameter.data, parameter.condition, parameter.table).then(responseData =>{
                        if(responseData.success && responseData.data){
                            resultData.success = true;
                            resultData.message = 'Lưu thành công!';
                        }else{
                            resultData.message = responseData.message;
                        }
                        res.json(resultData);
                    })
                }
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
        createHepatitis: function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: "",
                    data: ''
                };
                // new Date().toLocaleDateString('fr-CA');
                const validateRules = [
                //     { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
                //     { field: "ma_benh_an", type: "string", required: true, message: "Vui lòng nhập mã bệnh án!" }
                ];
                const parameter = hepatitis.getDataBodyHepatitis(req.body, req.params.type);
                const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                    return res.json(resultMessage);
                }
                if(errors.length > 0){
                    resultData.message = errors.map(s => s.message).join(', ');
                    return res.json(resultData);
                }else{
                    parameter.data['patient_id'] = req.params.patient_id;
                    parameter.data['created_by'] = req.user.id;
                    parameter.data['campaign_id'] = req.user.campaign_id;
                    commonService.addRecordTable(parameter.data, parameter.table, true).then(responseData =>{
                        if(responseData.success && responseData.data){
                            resultData.success = true;
                            resultData.message = 'Lưu thành công!';
                            resultData.data = responseData.data;
                        }else{
                            resultData.message = responseData.message;
                        }
                        res.json(resultData);
                    })
                }
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
        getDataBodyHepatitis: function(body, type){
            switch(type){
                case 'dau-hieu-nhap-vien':
                    return {data: hepatitis.dauHieuNhapVien(body), table: 'viem_gan_mt1_dhnv', condition: {id: body.id ? body.id : ''}}
                case 'sga':
                    return {data: hepatitis.sga(body), table: 'viem_gan_mt1_sga', condition: {id: body.id ? body.id : ''}}
            }   
        },
        dauHieuNhapVien: function(body){
            return {
                chan_doan_benh: body.chan_doan_benh,
                nguyen_nhan: body.nguyen_nhan,
                nguyen_nhan_khac: body.nguyen_nhan_khac,
                cn: body.cn,
                cc: body.cc,
                vong_bap_chan: body.vong_bap_chan,
                got: body.got,
                gpt: body.gpt,
                hemoglobin: body.hemoglobin,
                bua_chinh: body.bua_chinh, 
                bua_phu: body.bua_phu,
                bua_phu_an: body.bua_phu_an,
                bua_phu_an_khac: body.bua_phu_an_khac,
                an_kieng: body.an_kieng,
                an_kieng_loai: body.an_kieng_loai,
                an_kieng_loai_khac: body.an_kieng_loai_khac,
                ruou_bia: body.ruou_bia,
                ruou_bia_ts: body.ruou_bia_ts,
                ml_ruou: body.ml_ruou,
                ml_bia: body.ml_bia,
                do_uong_khac: body.do_uong_khac,
                do_uong_khac_ts: body.do_uong_khac_ts,
                loai_do_uong: body.loai_do_uong,
                loai_do_uong_khac: body.loai_do_uong_khac,
                su_dung_la_cay: body.su_dung_la_cay,
                loai_la_cay: body.loai_la_cay,
                note: body.note
            }
        },
        sga: function(body){
            return {
                cn_6_thang: body.cn_6_thang,
                cn_2_tuan: body.cn_2_tuan,
                khau_phan_an_ht: body.khau_phan_an_ht,
                tieu_chung_th: body.tieu_chung_th,
                giam_chuc_nang: body.giam_chuc_nang,
                nc_chuyen_hoa: body.nc_chuyen_hoa,
                mo_duoi_da: body.mo_duoi_da,
                teo_co: body.teo_co,
                phu: body.phu,
                co_chuong: body.co_chuong,
                phan_loai: body.phan_loai,
                time_id: body.time_id
            }
        },
        addTimes: function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: "",
                    insertId: ''
                };
                const validateRules = [
                    { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
                ];
                const parameter = {
                    patient_id: req.params.patient_id,
                    time: req.body.time,
                    type: req.params.type,
                    project:'viem-gan-mt1',
                    created_by: req.user.id,
                    campaign_id: req.user.campaign_id
                };
                const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                    return res.json(resultMessage);
                }
                if(errors.length > 0){
                    resultData.message = errors.map(s => s.message).join(', ');
                    return res.json(resultData);
                }else{
                    commonService.addRecordTable(parameter, 'times', true).then(responseData =>{
                        if(responseData.success && responseData.data){
                            resultData.success = true;
                            resultData.message = 'Thành công!';
                            resultData.insertId = responseData.data.insertId;
                        }else{
                            resultData.message = responseData.message;
                        }
                        res.json(resultData);
                    })
                }
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
        updateTimes: function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: ""
                };
                const validateRules = [
                    { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" },
                    { field: "timeActive", type: "string", required: true, message: "Vui lòng chọn ngày được sửa!" },
                ];
                const parameter = {
                    time: req.body.time,
                    timeActive: req.body.timeActive
                };
                const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultMessage.error = 'Bạn không có quyền sửa danh sách này!';
                    return res.json(resultMessage);
                }
                if(errors.length > 0){
                    resultData.message = errors.map(s => s.message).join(', ');
                    return res.json(resultData);
                }else{
                    delete parameter.timeActive;
                    commonService.updateRecordTable(parameter, {id: req.body.timeActive},'times').then(responseData =>{
                        if(responseData.success && responseData.data){
                            resultData.success = true;
                            resultData.message = 'Thành công!';
                        }else{
                            resultData.message = responseData.message;
                        }
                        res.json(resultData);
                    })
                }
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
        dataTime:function(req, res){
            try {
                var resultData = {
                    success: false,
                    message: "",
                    data: {}
                };
        
                let table = '';
                switch(req.params.type){
                    case 'sga': table = 'viem_gan_mt1_sga'
                        break;
                    default: break
                }
                if(!req.user.role_id.includes(6) && !req.user.isAdmin){
                    resultMessage.error = 'Bạn không có quyền truy cập danh sách này!';
                    return res.json(resultMessage);
                }
                commonService.getAllDataTable(table, securityService.applyRoleBasedFiltering(req.user, {patient_id: req.params.patient_id, time_id: req.body.time_id})).then(responseData =>{
                    if(responseData.success){
                        resultData.success = true;
                        if(responseData.data && responseData.data.length > 0){
                            resultData.data = responseData.data[0];
                        }else{
                            resultData.message = 'Không có dữ liệu';
                        }
                    }else{
                        resultData.message = responseData.message;
                    }
                    return res.json(resultData);
                })
            } catch (error) {
                commonService.saveLog(req, error.message, error.stack);
                res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
            }
        },
    }

module.exports = hepatitis;