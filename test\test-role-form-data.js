// File test để kiểm tra xử lý role từ form data
function testRoleFormData() {
    console.log('=== Testing Role Form Data Processing ===');
    
    // Test 1: Simulate req.body.role (array from AJAX)
    console.log('\n1. Testing req.body.role (array from AJAX)...');
    const reqBody1 = {
        fullname: 'Test User',
        email: '<EMAIL>',
        role: [1, 2, 3]
    };
    
    let role_ids1 = [];
    if (reqBody1.role) {
        if (Array.isArray(reqBody1.role)) {
            role_ids1 = reqBody1.role;
        } else {
            role_ids1 = [parseInt(reqBody1.role)];
        }
    } else if (reqBody1['role[]']) {
        if (Array.isArray(reqBody1['role[]'])) {
            role_ids1 = reqBody1['role[]'].map(r => parseInt(r));
        } else {
            role_ids1 = [parseInt(reqBody1['role[]'])];
        }
    }
    
    console.log('reqBody1.role:', reqBody1.role);
    console.log('role_ids1:', role_ids1);
    
    // Test 2: Simulate req.body['role[]'] (from form serialize)
    console.log('\n2. Testing req.body[\'role[]\'] (from form serialize)...');
    const reqBody2 = {
        fullname: 'Test User',
        email: '<EMAIL>',
        'role[]': ['3', '4'] // String array from form
    };
    
    let role_ids2 = [];
    if (reqBody2.role) {
        if (Array.isArray(reqBody2.role)) {
            role_ids2 = reqBody2.role;
        } else {
            role_ids2 = [parseInt(reqBody2.role)];
        }
    } else if (reqBody2['role[]']) {
        if (Array.isArray(reqBody2['role[]'])) {
            role_ids2 = reqBody2['role[]'].map(r => parseInt(r));
        } else {
            role_ids2 = [parseInt(reqBody2['role[]'])];
        }
    }
    
    console.log('reqBody2[\'role[]\']:', reqBody2['role[]']);
    console.log('role_ids2:', role_ids2);
    
    // Test 3: Simulate single role value
    console.log('\n3. Testing single role value...');
    const reqBody3 = {
        fullname: 'Test User',
        email: '<EMAIL>',
        role: '2'
    };
    
    let role_ids3 = [];
    if (reqBody3.role) {
        if (Array.isArray(reqBody3.role)) {
            role_ids3 = reqBody3.role;
        } else {
            role_ids3 = [parseInt(reqBody3.role)];
        }
    } else if (reqBody3['role[]']) {
        if (Array.isArray(reqBody3['role[]'])) {
            role_ids3 = reqBody3['role[]'].map(r => parseInt(r));
        } else {
            role_ids3 = [parseInt(reqBody3['role[]'])];
        }
    }
    
    console.log('reqBody3.role:', reqBody3.role);
    console.log('role_ids3:', role_ids3);
    
    // Test 4: Simulate no role
    console.log('\n4. Testing no role...');
    const reqBody4 = {
        fullname: 'Test User',
        email: '<EMAIL>'
    };
    
    let role_ids4 = [];
    if (reqBody4.role) {
        if (Array.isArray(reqBody4.role)) {
            role_ids4 = reqBody4.role;
        } else {
            role_ids4 = [parseInt(reqBody4.role)];
        }
    } else if (reqBody4['role[]']) {
        if (Array.isArray(reqBody4['role[]'])) {
            role_ids4 = reqBody4['role[]'].map(r => parseInt(r));
        } else {
            role_ids4 = [parseInt(reqBody4['role[]'])];
        }
    }
    
    console.log('reqBody4.role:', reqBody4.role);
    console.log('role_ids4:', role_ids4);
    console.log('role_ids4.length:', role_ids4.length);
    console.log('Should show error:', !role_ids4 || role_ids4.length === 0);
    
    console.log('\n=== Role Form Data Processing Test Completed ===');
}

// Chạy test
testRoleFormData(); 