<!DOCTYPE html>
<html lang="en">

<head>
    <%- include('../layout/head') %>
        <title>Admin</title>
</head>

<body>
    <!-- Page Wrapper -->
    <div id="wrapper">
        <%- include('../layout/sidebar') %>
            <!-- Content Wrapper -->
            <div id="content-wrapper" class="d-flex flex-column">
                <!-- Main Content -->
                <div id="content">
                    <%- include('../layout/header') %>
                    <%if(errors.length > 0){%>
                        <div class="container">
                            <div class="box mt-3">
                                <%for(let item of errors){%>
                                <div><%-JSON.stringify(item)%></div>
                                <%}%>
                            </div>
                        </div>
                    <%}else{%>
                        <div class="container-fluid">
                            <div class="card shadow mt-3">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">Danh sách thực đơn mẫu</h6>
                                    <a class="btn btn-success btn-circle" onclick="openModalCreateTable('menu_example')">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Tên thực đơn</th>
                                                    <th>Ngày tạo</th>
                                                    <th>Người tạo</th>
                                                    <th>Trạng thái</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <% } %>
                </div>
                <%- include('../layout/footer') %>
            </div>
    </div>
    <div class="modal fade" id="modal-add-menu-example" tabindex="-1" aria-hidden="true">
        <input type="hidden" id="menu_example_select_edit">
        <div class="modal-dialog modal-dialog-centered modal-xl">
          <div class="modal-content">
            <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            <h3 class="modal-title fs-6 text-uppercase text-center mb-3">Thêm thực đơn mẫu</h3>
            <div class="row flex-wrap g-3">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tên thực đơn</h6>
                        </div>
                        <div class="card-body">
                            <input type="text" class="form-control" id="name_menu_modal" placeholder="Nhập tên thực đơn">
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Chia sẻ</h6>
                        </div>
                        <div class="card-body">
                            <div data-plugin="virtual-select" data-config='{"placeholder":"Chọn trạng thái chia sẻ"}' id="share"
                                data-options='[{"label":"Riêng tư","value":"0"},{"label":"Chia sẻ","value":"1"}]'></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-2 justify-content-center mt-2">
              <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button"  data-bs-dismiss="modal">Huỷ</button>
              </div>
              <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="addDataTable('menu_example')">Lưu</button>
              </div>
            </div>
          </div>
        </div>
    </div>
    
    <script>
        window.listMenuTime = <%-JSON.stringify(menuTime)%>;
    </script>
    
    <script>
        $(document).ready(function () {
            dataTable = $('#dataTable').DataTable({
                dom: '<"top d-flex flex-wrap gap-2 justify-content-between align-items-center mb-2"pf>rt<"bottom d-flex flex-wrap gap-2 justify-content-between align-items-center mt-2"il><"clear">',
                serverSide: true,
                processing: true,
                responsive: true,
                pageLength: 25,
                lengthMenu: [25, 50, 75, 100],
                paging: true,
                scrollX: true,
                ajax: {
                    url: `/admin/thuc-don-mau/list/`,
                    method: 'POST',
                    dataType: "json",
                    beforeSend: function() {
                        loading.show();
                    },
                    complete: function() {  // Thêm complete để ẩn loading khi xong
                        loading.hide();
                    },
                    dataSrc: function(response){
                        if (response.data) {
                            return response.data;  // Trả về mảng dữ liệu
                        } else {
                            return [];  // Trả về mảng rỗng nếu không có dữ liệu
                        }
                    },
                },
                rowId: function(row) {
                    return  'row-' + row.id; // Thêm "row-" vào trước giá trị id
                },
                columns: [
                    { data: 'name_menu' },
                    {   
                        data: 'created_at', // Cột ngày tháng
                        render: function(data, type, row) {
                            // Kiểm tra nếu có Moment.js
                            return data ? moment(data).format('DD/MM/YYYY') : ''; // Định dạng ngày giờ
                        }
                    },
                    { data: 'created_by_name' },
                    { 
                        data: 'share',
                        render: function(data, type, row){
                            switch(data){
                                case 0: return 'Riêng tư';
                                case 1: return 'Chia sẻ';
                                default: return 'Riêng tư';
                            }
                        }
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return `
                                <div class="d-flex gap-2">
                                    <a class="btn btn-info btn-sm btn-circle" href="/admin/thuc-don-mau/${row.id}" title="Chi tiết"><i class="fas fa-eye"></i></a>
                                    <a class="btn btn-warning btn-sm btn-circle" data-id="${row.id}" onclick="openModalEditTable('menu_example', ${row.id})" title="Sửa"><i class="fas fa-pen-square"></i></a>
                                    <button class="btn btn-danger btn-sm btn-circle" data-id="${row.id}" onclick="deleteMenuExample(${row.id},'${row.name_menu}')" title="Xóa"><i class="fas fa-trash"></i></button>
                                </div>
                            `;
                        },
                    },
                ]
            });
        });

    </script>
</body>

</html>