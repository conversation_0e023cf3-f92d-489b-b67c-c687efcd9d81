<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dish Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <link href="/vendor/virtual-select/virtual-select.min.css" rel="stylesheet">
    <link href="/css/dish-admin.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Form Món Ăn</h2>
        
        <!-- Test Virtual Select -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Virtual Select</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="foodType">Loại thực phẩm</label>
                        <select class="form-control" id="foodType">
                            <option value="">Tất cả</option>
                            <option value="raw">Sống</option>
                            <option value="cooked">Chín</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="foodYear">Năm dữ liệu</label>
                        <select class="form-control" id="foodYear">
                            <option value="">Tất cả</option>
                            <option value="2017">2017</option>
                            <option value="2024">2024</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="foodSelect">Chọn thực phẩm</label>
                        <div id="foodSelect"></div>
                    </div>
                    <div class="col-md-2">
                        <label for="foodWeight">Khối lượng (g)</label>
                        <input type="number" class="form-control" id="foodWeight" 
                               placeholder="100" min="0.1" step="0.1">
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-success" onclick="testAddFood()">
                        <i class="fas fa-plus"></i> Test Thêm thực phẩm
                    </button>
                    <button type="button" class="btn btn-info ml-2" onclick="testAPI()">
                        <i class="fas fa-search"></i> Test API
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Kết quả Test</h5>
            </div>
            <div class="card-body">
                <pre id="testResults">Chưa có kết quả test nào...</pre>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="/vendor/virtual-select/virtual-select.min.js"></script>

    <script>
        // Mock data
        window.dishData = {
            isEdit: false,
            existingFoods: []
        };

        let dishFoods = [];
        let foodSelectInstance = null;

        $(document).ready(function() {
            log('Initializing test page...');
            initFoodSelect();
        });

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const current = $('#testResults').text();
            $('#testResults').text(`[${timestamp}] ${message}\n${current}`);
            console.log(message);
        }

        function initFoodSelect() {
            try {
                log('Initializing Virtual Select...');
                
                VirtualSelect.init({
                    ele: '#foodSelect',
                    placeholder: 'Tìm kiếm thực phẩm...',
                    search: true,
                    searchPlaceholderText: 'Nhập tên thực phẩm (tối thiểu 2 ký tự)...',
                    noSearchResultsText: 'Không tìm thấy thực phẩm nào',
                    noOptionsText: 'Nhập từ khóa để tìm kiếm',
                    optionsCount: 10,
                    onServerSearch: function(search, virtualSelect) {
                        log(`Searching for: "${search}"`);
                        
                        if (search.length < 2) {
                            virtualSelect.setServerOptions([]);
                            return;
                        }

                        const type = $('#foodType').val();
                        const type_year = $('#foodYear').val();
                        
                        log(`Search params: search="${search}", type="${type}", type_year="${type_year}"`);
                        
                        $.ajax({
                            url: '/api/food-search',
                            type: 'GET',
                            data: {
                                search: search,
                                type: type,
                                type_year: type_year
                            },
                            success: function(response) {
                                log(`API Response: ${JSON.stringify(response, null, 2)}`);
                                
                                if (response.success && response.data) {
                                    const options = response.data.map(food => ({
                                        label: food.name + ' (' + (food.type === 'raw' ? 'Sống' : 'Chín') + ' - ' + food.type_year + ')',
                                        value: food.id,
                                        customData: food
                                    }));
                                    log(`Mapped ${options.length} options`);
                                    virtualSelect.setServerOptions(options);
                                } else {
                                    log('No data in response');
                                    virtualSelect.setServerOptions([]);
                                }
                            },
                            error: function(xhr, status, error) {
                                log(`API Error: ${status} - ${error}`);
                                virtualSelect.setServerOptions([]);
                            }
                        });
                    }
                });
                
                foodSelectInstance = document.querySelector('#foodSelect');
                log('Virtual Select initialized successfully');
                
            } catch (error) {
                log(`Error initializing Virtual Select: ${error.message}`);
            }
        }

        function testAddFood() {
            log('Testing addFoodToDish...');
            
            const selectedOptions = foodSelectInstance?.getSelectedOptions();
            const weightInput = $('#foodWeight').val();
            
            log(`Selected options: ${JSON.stringify(selectedOptions)}`);
            log(`Weight input: "${weightInput}"`);
            
            if (!selectedOptions || selectedOptions.length === 0) {
                log('ERROR: No food selected');
                toastr.error('Vui lòng chọn thực phẩm!');
                return;
            }

            if (!weightInput || weightInput.trim() === '') {
                log('ERROR: No weight entered');
                toastr.error('Vui lòng nhập khối lượng!');
                return;
            }

            const weight = parseFloat(weightInput);
            if (isNaN(weight) || weight <= 0) {
                log(`ERROR: Invalid weight: ${weightInput}`);
                toastr.error('Khối lượng phải là số dương!');
                return;
            }

            const foodData = selectedOptions.customData;
            log(`Food data: ${JSON.stringify(foodData)}`);
            
            if (!foodData || !foodData.id) {
                log('ERROR: Invalid food data');
                toastr.error('Dữ liệu thực phẩm không hợp lệ!');
                return;
            }

            log('SUCCESS: All validations passed!');
            toastr.success(`Thành công! Thêm "${foodData.name}" với khối lượng ${weight}g`);
        }

        function testAPI() {
            log('Testing API directly...');
            
            $.ajax({
                url: '/api/food-search',
                type: 'GET',
                data: {
                    search: 'gạo',
                    type: '',
                    type_year: ''
                },
                success: function(response) {
                    log(`Direct API test success: ${JSON.stringify(response, null, 2)}`);
                },
                error: function(xhr, status, error) {
                    log(`Direct API test error: ${status} - ${error}`);
                }
            });
        }
    </script>
</body>
</html> 