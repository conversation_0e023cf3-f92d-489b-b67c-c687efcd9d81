# Test Phân Quyền Danh Sách Dữ Liệu

## Tóm tắt thay đổi

Đã thực hiện phân quyền cho các controller để:
- **User thường**: Chỉ xem được dữ liệu do họ tạo (theo trường `created_by`)
- **Admin**: Xem được tất cả dữ liệu

## Các controller đã được cập nhật

### 1. hepatitisController
- **Bảng**: `viem_gan_td_not`, `viem_gan_td_ngt`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 2. tetanusController  
- **Bảng**: `uon_van_kpa`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 3. liverSurgeryController
- **<PERSON><PERSON>ng**: `cat_gan_nho_kpa`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 4. researchController
- **Bảng**: `research`, `patients_research`
- **Method**: `getListTable`, `patientList`
- **Thay đổi**: Thêm `user: req.user` vào parameter

## Các service đã được cập nhật

### 1. commonService.getAllBoarding()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

### 2. commonService.countAllBoarding()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

### 3. commonService.getDataTableData()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

## Kiểm tra cần thực hiện

### Test Case 1: Admin User
1. Đăng nhập với tài khoản admin
2. Truy cập các danh sách:
   - Viêm gan: `/viem-gan/{patient_id}/che-do-an-noi-tru`
   - Uốn ván: `/uon-van-list/{patient_id}/kpa`
   - Cắt gan nhỏ: `/hoi-chan-list/{patient_id}/kpa`
   - Nghiên cứu: `/research-list`
3. **Kết quả mong đợi**: Xem được tất cả dữ liệu

### Test Case 2: User thường
1. Đăng nhập với tài khoản user thường
2. Truy cập các danh sách tương tự
3. **Kết quả mong đợi**: Chỉ xem được dữ liệu do user đó tạo

### Test Case 3: Dữ liệu trống
1. Đăng nhập với user mới (chưa tạo dữ liệu nào)
2. Truy cập các danh sách
3. **Kết quả mong đợi**: Danh sách trống

## Cấu trúc bảng đã kiểm tra

Tất cả các bảng đều đã có trường `created_by`:
- ✅ `research` 
- ✅ `patients_research`
- ✅ `uon_van_kpa`
- ✅ `viem_gan_td_ngt`
- ✅ `viem_gan_td_not`
- ✅ `cat_gan_nho_kpa`

## Lưu ý quan trọng

1. **Dữ liệu cũ**: Các bản ghi có `created_by = NULL` sẽ không hiển thị cho user thường
2. **Tạo dữ liệu mới**: Cần đảm bảo khi tạo mới, trường `created_by` được set đúng
3. **Backup**: Nên backup database trước khi test
4. **Rollback**: Có thể rollback bằng cách xóa điều kiện `created_by` trong các method service

## Các file đã thay đổi

### Services
1. `services/commonService.js`
   - Thêm logic phân quyền vào `getAllBoarding()`, `countAllBoarding()`, `getDataTableData()`
   - Thêm helper function `checkDataOwnership()` để kiểm tra quyền sở hữu dữ liệu

### Controllers - Đã cập nhật đầy đủ
2. `controllers/hepstitisMt1Controller.js`
   - ✅ `createHepatitis()` - có kiểm tra quyền và set created_by
   - ✅ `editHepatitis()` - thêm kiểm tra quyền sở hữu dữ liệu
   - ✅ `deleteTime()` - thêm kiểm tra quyền sở hữu dữ liệu

3. `controllers/standardController.js`
   - ✅ `createStandard()` - có kiểm tra quyền và set created_by
   - ✅ `editStandard()` - thêm kiểm tra quyền sở hữu dữ liệu
   - ✅ `deleteTime()` - thêm kiểm tra quyền sở hữu dữ liệu

### Controllers - Cập nhật một phần
4. `controllers/hepatitisController.js`
   - ✅ Danh sách: `getListTable()` - thêm phân quyền
   - ✅ `createHepatitis()` - có kiểm tra quyền và set created_by
   - ✅ `editHepatitis()` - thêm kiểm tra quyền sở hữu dữ liệu
   - ⚠️ `addBroading()`, `updateBroading()`, `deleteBroading()` - cần cập nhật

5. `controllers/tetanusController.js`
   - ✅ Danh sách: `getListTable()` - thêm phân quyền
   - ⚠️ Các method thêm/sửa/xóa khác - cần kiểm tra và cập nhật

6. `controllers/liverSurgeryController.js`
   - ✅ Danh sách: `getListTable()` - thêm phân quyền
   - ⚠️ Các method thêm/sửa/xóa khác - cần kiểm tra và cập nhật

7. `controllers/researchController.js`
   - ✅ Danh sách: `getListTable()`, `patientList()` - thêm phân quyền
   - ⚠️ Các method thêm/sửa/xóa khác - cần kiểm tra và cập nhật

## Cần hoàn thiện

### 1. Cập nhật các method còn lại
Cần kiểm tra và cập nhật tất cả method thêm/sửa/xóa trong:
- hepatitisController: `addBroading()`, `updateBroading()`, `deleteBroading()`
- tetanusController: tất cả method thêm/sửa/xóa
- liverSurgeryController: tất cả method thêm/sửa/xóa
- researchController: tất cả method thêm/sửa/xóa

### 2. Sử dụng helper function
Thay thế code kiểm tra quyền thủ công bằng helper function `commonService.checkDataOwnership()`:

```javascript
// Thay vì:
const checkOwnership = await commonService.getAllDataTable(table, {id: recordId});
if(checkOwnership.success && checkOwnership.data && checkOwnership.data.length > 0){
    const record = checkOwnership.data[0];
    if(record.created_by && record.created_by !== req.user.id){
        // error
    }
}

// Sử dụng:
const ownershipCheck = await commonService.checkDataOwnership(table, recordId, req.user.id, req.user.isAdmin);
if(!ownershipCheck.hasPermission){
    resultData.message = ownershipCheck.message;
    return res.json(resultData);
}
```

### 3. Routes đã có middleware authentication
Routes trong `routes/index.js` đã có middleware cơ bản:
- `commonService.isAuthenticated` - cho GET requests
- `commonService.isAuthenticatedPost` - cho POST requests
- `commonService.isAuthenticatedPostList` - cho POST list requests

Một số routes đã có middleware phân quyền:
```javascript
router.get("/viem-gan", commonService.isAuthenticated, securityService.requirePermission('viem-gan', 'read'), patient.getlist);
```

### 4. Cần thêm middleware phân quyền cho routes còn lại
Cần thêm `securityService.requirePermission()` cho các routes:

```javascript
// Viêm gan
router.post("/viem-gan-create/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('viem-gan', 'write'),
    hepatitis.createHepatitis);

router.post("/viem-gan-update/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('viem-gan', 'write'),
    hepatitis.editHepatitis);

// Tương tự cho tetanus, liverSurgery, research, standard...
```

## Trạng thái hiện tại

✅ **Đã hoàn thành:**
- Phân quyền danh sách (user thường chỉ xem dữ liệu created_by họ)
- Một số method thêm/sửa/xóa đã có kiểm tra quyền sở hữu
- Helper function `checkDataOwnership()` đã sẵn sàng

⚠️ **Cần hoàn thiện:**
- Cập nhật tất cả method thêm/sửa/xóa còn lại
- Thêm middleware phân quyền vào routes
- Sử dụng helper function thống nhất

🎯 **Kết quả mong đợi:**
- User thường: chỉ thao tác với dữ liệu do họ tạo
- Admin: thao tác với tất cả dữ liệu
- Báo lỗi rõ ràng khi không có quyền
