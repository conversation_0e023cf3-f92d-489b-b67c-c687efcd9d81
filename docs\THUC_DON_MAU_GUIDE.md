# Hướng dẫn sử dụng tính năng Thực đơn mẫu

## Tổng quan
Tính năng này cho phép quản lý thực đơn mẫu trong hệ thống admin, bao gồm:
- <PERSON><PERSON> sách thực đơn mẫu (giống với danh sách user)
- <PERSON> tiết thực đơn mẫu (giống với khẩu phần ăn)
- Sử dụng chung file JS menuExample.js

## Cấu trúc files đã thay đổi

### Views
1. `views/admin/thuc-don-mau/list.ejs` - <PERSON>h sách thực đơn mẫu
2. `views/admin/thuc-don-mau/index.ejs` - <PERSON> tiết thực đơn mẫu

### Controllers
1. `controllers/adminController.js` - Thêm các methods:
   - `menuExampleList()` - Hiển thị danh sách
   - `menuExampleDetail()` - Hi<PERSON>n thị chi tiết
   - `menuExampleListData()` - API lấy dữ liệu danh sách
   - `menuExampleUpsert()` - API thêm/sửa
   - `menuExampleDelete()` - API xóa

### Routes
1. `routes/admin.js` - Thêm các routes:
   - `GET /admin/thuc-don-mau` - Danh sách
   - `GET /admin/thuc-don-mau/:id` - Chi tiết
   - `POST /admin/thuc-don-mau/list` - API danh sách
   - `POST /admin/thuc-don-mau/upsert/` - API thêm/sửa
   - `POST /admin/thuc-don-mau/delete/:id` - API xóa

### JavaScript
1. `public/js/admin.js` - Thêm các functions:
   - `deleteMenuExample()`
   - `getDataMenuExample()`
   - `resetFormMenuExample()`
   - `setDataFormMenuExample()`

## Database
Sử dụng bảng `menu_example` với cấu trúc:
- `id` - ID thực đơn
- `name_menu` - Tên thực đơn
- `detail` - Chi tiết thực đơn (JSON string)
- `share` - Trạng thái chia sẻ (0: riêng tư, 1: chia sẻ)
- `created_by` - Người tạo
- `created_at` - Ngày tạo

## Cách sử dụng

### 1. Truy cập danh sách thực đơn mẫu
- URL: `/admin/thuc-don-mau`
- Hiển thị danh sách với các cột: Tên thực đơn, Ngày tạo, Người tạo, Trạng thái
- Có các nút: Thêm mới, Chi tiết, Sửa, Xóa

### 2. Thêm mới thực đơn mẫu
- Click nút "+" để mở modal thêm mới
- Nhập tên thực đơn và chọn trạng thái chia sẻ
- Click "Lưu" để tạo thực đơn mẫu trống

### 3. Chi tiết thực đơn mẫu
- Click nút "Chi tiết" (icon mắt) để vào trang chi tiết
- Trang chi tiết giống với khẩu phần ăn, cho phép:
  - Chọn thực đơn mẫu có sẵn để thêm vào
  - Tạo mới thực đơn trống
  - Thêm thực phẩm vào từng giờ ăn
  - Lưu thực đơn mẫu

### 4. JSON Structure cho detail
```json
[
  {
    "id": 3,
    "name": "6h - 6h30",
    "name_course": "",
    "listFood": [
      {
        "id": 1,
        "id_food": 37,
        "name": "Bún",
        "weight": 200,
        "energy": 220,
        "protein": 3.4,
        "animal_protein": 0,
        "lipid": 0,
        "unanimal_lipid": 0,
        "carbohydrate": 51.4
      }
    ]
  }
]
```

## Lưu ý
- Sử dụng chung file `menuExample.js` với khẩu phần ăn
- Cần có dữ liệu trong bảng `menu_time` và `food_info`
- Chỉ admin mới có quyền truy cập và quản lý 