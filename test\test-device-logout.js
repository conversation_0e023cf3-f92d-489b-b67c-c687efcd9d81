const axios = require('axios');

// Cấ<PERSON>ình test
const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';

// Test data
let authToken = null;
let devices = [];

async function testDeviceLogout() {
    console.log('🧪 Bắt đầu test tính năng Logout từng thiết bị...\n');

    try {
        // 1. Đăng nhập để lấy token
        console.log('1️⃣ Đăng nhập để lấy token...');
        const loginResponse = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        });

        if (loginResponse.data.success) {
            console.log('✅ Đăng nhập thành công');
            authToken = loginResponse.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
        } else {
            console.log('❌ Đăng nhập thất bại:', loginResponse.data.message);
            return;
        }

        // 2. Lấy danh sách thiết bị
        console.log('\n2️⃣ Lấy danh sách thiết bị...');
        const devicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (devicesResponse.data.success) {
            devices = devicesResponse.data.data.devices;
            console.log(`✅ Tìm thấy ${devices.length} thiết bị đang hoạt động`);
            
            devices.forEach((device, index) => {
                console.log(`   ${index + 1}. ${device.deviceName} (${device.deviceType}) - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
                console.log(`      Browser: ${device.browser}, OS: ${device.os}, IP: ${device.ipAddress}`);
                console.log(`      Hoạt động: ${device.timeAgo}, Có thể logout: ${device.canLogout}`);
            });
        } else {
            console.log('❌ Không thể lấy danh sách thiết bị');
            return;
        }

        // 3. Test logout thiết bị hiện tại (sẽ bị từ chối)
        console.log('\n3️⃣ Test logout thiết bị hiện tại (sẽ bị từ chối)...');
        const currentDevice = devices.find(d => d.isCurrentSession);
        if (currentDevice) {
            const logoutCurrentResponse = await axios.post(`${BASE_URL}/devices/logout`, {
                tokenId: currentDevice.tokenId
            }, {
                headers: { Cookie: `token=${authToken}` }
            });

            if (logoutCurrentResponse.data.success) {
                console.log('❌ Lỗi: Có thể logout thiết bị hiện tại');
            } else {
                console.log('✅ Đúng: Không thể logout thiết bị hiện tại');
                console.log(`   Lý do: ${logoutCurrentResponse.data.message}`);
            }
        }

        // 4. Test logout thiết bị khác
        const otherDevices = devices.filter(d => !d.isCurrentSession);
        if (otherDevices.length > 0) {
            console.log(`\n4️⃣ Test logout thiết bị khác (${otherDevices.length} thiết bị)...`);
            
            for (let i = 0; i < Math.min(otherDevices.length, 2); i++) {
                const device = otherDevices[i];
                console.log(`   Logout thiết bị: ${device.deviceName}...`);
                
                const logoutResponse = await axios.post(`${BASE_URL}/devices/logout`, {
                    tokenId: device.tokenId
                }, {
                    headers: { Cookie: `token=${authToken}` }
                });

                if (logoutResponse.data.success) {
                    console.log(`   ✅ Đã logout thiết bị ${device.deviceName} thành công`);
                    console.log(`   Thông tin: ${logoutResponse.data.message}`);
                } else {
                    console.log(`   ❌ Lỗi khi logout thiết bị ${device.deviceName}: ${logoutResponse.data.message}`);
                }
            }
        } else {
            console.log('\n4️⃣ Không có thiết bị khác để test logout');
        }

        // 5. Kiểm tra lại danh sách thiết bị sau khi logout
        console.log('\n5️⃣ Kiểm tra lại danh sách thiết bị...');
        const updatedDevicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (updatedDevicesResponse.data.success) {
            const updatedDevices = updatedDevicesResponse.data.data.devices;
            console.log(`✅ Còn lại ${updatedDevices.length} thiết bị đang hoạt động`);
            
            updatedDevices.forEach((device, index) => {
                console.log(`   ${index + 1}. ${device.deviceName} (${device.deviceType}) - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
            });
        }

        // 6. Test logout tất cả thiết bị khác
        console.log('\n6️⃣ Test logout tất cả thiết bị khác...');
        const logoutAllResponse = await axios.post(`${BASE_URL}/devices/logout-all-others`, {}, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (logoutAllResponse.data.success) {
            console.log(`✅ ${logoutAllResponse.data.message}`);
            console.log(`   Số thiết bị đã logout: ${logoutAllResponse.data.count}`);
        } else {
            console.log('❌ Lỗi khi logout tất cả thiết bị khác:', logoutAllResponse.data.message);
        }

        // 7. Kiểm tra danh sách cuối cùng
        console.log('\n7️⃣ Kiểm tra danh sách cuối cùng...');
        const finalDevicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: { Cookie: `token=${authToken}` }
        });

        if (finalDevicesResponse.data.success) {
            const finalDevices = finalDevicesResponse.data.data.devices;
            console.log(`✅ Danh sách cuối cùng: ${finalDevices.length} thiết bị`);
            
            if (finalDevices.length === 1 && finalDevices[0].isCurrentSession) {
                console.log('✅ Hoàn hảo: Chỉ còn lại thiết bị hiện tại');
            } else {
                console.log('⚠️  Vẫn còn thiết bị khác hoạt động');
            }
        }

        console.log('\n🎉 Test hoàn thành!');

    } catch (error) {
        console.error('❌ Lỗi trong quá trình test:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Test các trường hợp lỗi
async function testErrorCases() {
    console.log('\n🧪 Test các trường hợp lỗi...\n');

    try {
        // Test 1: Không có token
        console.log('1️⃣ Test logout không có token...');
        try {
            await axios.post(`${BASE_URL}/devices/logout`, { tokenId: 'fake-token' });
            console.log('❌ Lỗi: Có thể logout mà không cần token');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ Đúng: Yêu cầu đăng nhập để logout');
            } else {
                console.log('❌ Lỗi không mong muốn:', error.message);
            }
        }

        // Test 2: Token ID không hợp lệ
        console.log('\n2️⃣ Test logout với token ID không hợp lệ...');
        const loginResponse = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        });

        if (loginResponse.data.success) {
            const authToken = loginResponse.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
            
            const invalidLogoutResponse = await axios.post(`${BASE_URL}/devices/logout`, {
                tokenId: 'invalid-token-id'
            }, {
                headers: { Cookie: `token=${authToken}` }
            });

            if (invalidLogoutResponse.data.success) {
                console.log('❌ Lỗi: Có thể logout với token ID không hợp lệ');
            } else {
                console.log('✅ Đúng: Không thể logout với token ID không hợp lệ');
                console.log(`   Lý do: ${invalidLogoutResponse.data.message}`);
            }
        }

        // Test 3: Không cung cấp tokenId
        console.log('\n3️⃣ Test logout không cung cấp tokenId...');
        const loginResponse2 = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token'
        });

        if (loginResponse2.data.success) {
            const authToken2 = loginResponse2.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
            
            const noTokenResponse = await axios.post(`${BASE_URL}/devices/logout`, {}, {
                headers: { Cookie: `token=${authToken2}` }
            });

            if (noTokenResponse.data.success) {
                console.log('❌ Lỗi: Có thể logout mà không cung cấp tokenId');
            } else {
                console.log('✅ Đúng: Yêu cầu cung cấp tokenId');
                console.log(`   Lý do: ${noTokenResponse.data.message}`);
            }
        }

        console.log('\n🎉 Test lỗi hoàn thành!');

    } catch (error) {
        console.error('❌ Lỗi trong quá trình test lỗi:', error.message);
    }
}

// Chạy test
if (require.main === module) {
    testDeviceLogout().then(() => {
        return testErrorCases();
    });
}

module.exports = { testDeviceLogout, testErrorCases }; 