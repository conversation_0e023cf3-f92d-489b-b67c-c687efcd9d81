/**
 * Test Campaign Selector functionality
 * Run with: node test/test-campaign-selector.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testCampaignSelector() {
    console.log('🧪 Testing Campaign Selector functionality...\n');

    try {
        // Test 1: Login as admin
        console.log('1️⃣ Testing admin login...');
        const loginResponse = await axios.post(`${BASE_URL}/login`, {
            email: '<EMAIL>', // Replace with actual admin email
            password: 'admin123' // Replace with actual admin password
        });

        if (loginResponse.data.success) {
            console.log('✅ Admin login successful');
            
            // Extract token from response
            const cookies = loginResponse.headers['set-cookie'];
            const tokenCookie = cookies.find(cookie => cookie.startsWith('token='));
            const token = tokenCookie ? tokenCookie.split(';')[0] : '';
            
            // Test 2: Get campaign options
            console.log('\n2️⃣ Testing get campaign options...');
            const optionsResponse = await axios.get(`${BASE_URL}/admin/campaign/options`, {
                headers: {
                    Cookie: token
                }
            });

            if (optionsResponse.data.success) {
                console.log('✅ Campaign options retrieved successfully');
                console.log(`   Found ${optionsResponse.data.data.length} campaigns`);
                
                const campaigns = optionsResponse.data.data;
                if (campaigns.length > 0) {
                    // Test 3: Switch to first campaign
                    console.log('\n3️⃣ Testing campaign switch...');
                    const firstCampaign = campaigns[0];
                    
                    const switchResponse = await axios.post(`${BASE_URL}/admin/campaign/switch`, {
                        campaign_id: firstCampaign.value
                    }, {
                        headers: {
                            Cookie: token,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (switchResponse.data.success) {
                        console.log('✅ Campaign switch successful');
                        console.log(`   Switched to: ${switchResponse.data.data.campaign_name}`);
                        
                        // Test 4: Test data filtering (example with patients)
                        console.log('\n4️⃣ Testing data filtering...');
                        try {
                            const patientsResponse = await axios.get(`${BASE_URL}/viem-gan`, {
                                headers: {
                                    Cookie: token
                                }
                            });
                            
                            if (patientsResponse.status === 200) {
                                console.log('✅ Data filtering test passed (page loaded successfully)');
                            }
                        } catch (error) {
                            console.log('⚠️  Data filtering test skipped (page may require specific permissions)');
                        }
                        
                        // Test 5: Switch to another campaign
                        console.log('\n5️⃣ Testing switch to another campaign...');
                        if (campaigns.length > 1) {
                            const secondCampaign = campaigns[1];
                            const switchResponse2 = await axios.post(`${BASE_URL}/admin/campaign/switch`, {
                                campaign_id: secondCampaign.value
                            }, {
                                headers: {
                                    Cookie: token,
                                    'Content-Type': 'application/json'
                                }
                            });

                            if (switchResponse2.data.success) {
                                console.log('✅ Switch to another campaign successful');
                                console.log(`   Switched to: ${switchResponse2.data.data.campaign_name}`);
                            } else {
                                console.log('❌ Switch to another campaign failed:', switchResponse2.data.message);
                            }
                        } else {
                            console.log('⚠️  Only one campaign available, skipping second switch test');
                        }
                        
                    } else {
                        console.log('❌ Campaign switch failed:', switchResponse.data.message);
                    }
                } else {
                    console.log('⚠️  No campaigns found for testing');
                }
            } else {
                console.log('❌ Failed to get campaign options:', optionsResponse.data.message);
            }
        } else {
            console.log('❌ Admin login failed:', loginResponse.data.message);
        }

    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }

    console.log('\n🏁 Campaign Selector test completed');
}

// Test database schema
async function testDatabaseSchema() {
    console.log('\n🗄️ Testing database schema...');

    try {
        const commonService = require('../services/commonService');

        // Test if campaign_id column exists in user table
        const testQuery = `
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'user'
            AND COLUMN_NAME = 'campaign_id'
        `;

        const result = await commonService.getListTable(testQuery, []);

        if (result.success && result.data && result.data.length > 0) {
            console.log('✅ campaign_id column exists in user table');
        } else {
            console.log('❌ campaign_id column NOT found in user table');
        }

    } catch (error) {
        console.error('❌ Database schema test failed:', error.message);
    }
}

// Run tests
async function runAllTests() {
    console.log('🚀 Starting Campaign Selector Tests\n');
    
    await testDatabaseSchema();
    await testCampaignSelector();
    
    console.log('\n✨ All tests completed!');
    process.exit(0);
}

// Handle command line execution
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('Test suite failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testCampaignSelector,
    testDatabaseSchema
};
