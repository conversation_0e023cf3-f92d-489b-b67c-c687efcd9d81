# Hướng dẫn chuyển đổi từ Session sang JWT Authentication

## Tổng quan
Hệ thống đã được chuyển đổi từ Session-based authentication sang JWT (JSON Web Token) authentication với tính năng Single Device Login.

## Các thay đổi chính

### 1. Database Schema
- Thêm trường `jwt_token_id` vào bảng `user`
- Thêm trường `device_info` để lưu thông tin thiết bị
- Thêm trường `token_created_at` để lưu thời gian tạo token
- Tạo bảng `user_sessions` để lưu lịch sử đăng nhập (optional)

### 2. JWT Service (`services/jwtService.js`)
- Tạo và xác thực JWT token
- Quản lý token trong database cho single device login
- Lưu thông tin thiết bị
- Làm mới token tự động

### 3. Authentication Middleware
- `isAuthenticated`: Kiểm tra đăng nhập cho GET requests
- `isAuthenticatedPost`: Kiểm tra đăng nhập cho POST requests
- `isAuthenticatedPostList`: Kiểm tra đăng nhập cho DataTables

### 4. User Controller
- Đăng nhập: Tạo JWT token và lưu vào database (không dùng Passport Local Strategy)
- Đăng xuất: Xóa token khỏi database
- Single device login: Chỉ cho phép một thiết bị đăng nhập tại một thời điểm

## Cách triển khai

### Bước 1: Cập nhật Database
Chạy script SQL để cập nhật database schema:

```sql
-- Chạy file database/update_jwt_schema.sql
```

### Bước 2: Cài đặt Dependencies
Đảm bảo đã cài đặt các package cần thiết:

```bash
npm install jsonwebtoken
```

**Lưu ý:** `crypto` là module built-in của Node.js, không cần cài đặt riêng.

### Bước 3: Cấu hình Environment Variables
Thêm vào file `.env`:

```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

### Bước 4: Khởi động ứng dụng
```bash
npm start
```

### Bước 5: Test JWT Authentication (Optional)
```bash
node test-jwt.js
```

## Tính năng Single Device Login

### Cách hoạt động:
1. Khi user đăng nhập, hệ thống tạo một JWT token với `tokenId` duy nhất
2. Token ID được lưu vào database trong trường `jwt_token_id`
3. Mỗi request sẽ kiểm tra token có khớp với token trong database không
4. Nếu user đăng nhập ở thiết bị khác, token cũ sẽ bị vô hiệu hóa

### Lợi ích:
- Bảo mật cao hơn
- Kiểm soát được thiết bị đăng nhập
- Có thể theo dõi lịch sử đăng nhập
- Dễ dàng logout từ xa

## API Endpoints

### Đăng nhập
```
POST /login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password",
  "token": "recaptcha_token" // nếu bật reCAPTCHA
}
```

### Đăng xuất
```
GET /logout
```

### Kiểm tra trạng thái đăng nhập
```
GET /test-session
```

## Xử lý lỗi

### Các loại lỗi:
- `invalid_token`: Token không hợp lệ
- `other_device`: Tài khoản đã đăng nhập ở thiết bị khác
- `inactive_account`: Tài khoản chưa được kích hoạt

### Response format:
```json
{
  "success": false,
  "message": "Thông báo lỗi"
}
```

## Bảo mật

### JWT Token:
- Sử dụng secret key mạnh
- Token có thời hạn 24 giờ
- Tự động làm mới khi sắp hết hạn
- Lưu trong httpOnly cookie

### Single Device Login:
- Mỗi lần đăng nhập tạo token ID duy nhất
- Token cũ bị vô hiệu hóa khi đăng nhập mới
- Kiểm tra token trong database mỗi request

### Device Tracking:
- Lưu thông tin User-Agent
- Lưu địa chỉ IP
- Lưu thời gian đăng nhập

## Monitoring và Logging

### Session History:
- Bảng `user_sessions` lưu lịch sử đăng nhập
- Theo dõi thời gian login/logout
- Lưu thông tin thiết bị

### Telegram Notifications:
- Thông báo đăng nhập thành công/thất bại
- Thông báo tạo tài khoản mới

## Troubleshooting

### Lỗi thường gặp:

1. **"Unknown authentication strategy 'local'"**
   - **Nguyên nhân:** Đang sử dụng Passport Local Strategy nhưng đã chuyển sang JWT
   - **Giải pháp:** Đã cập nhật `userController.js` để xác thực thủ công thay vì dùng Passport Local Strategy

2. **"req.user.role_id is undefined" hoặc "Cannot read property 'includes' of undefined"**
   - **Nguyên nhân:** Trong JWT authentication, `req.user` có thể không có đầy đủ thông tin `role_id`
   - **Giải pháp:** 
     - Đã cập nhật JWT middleware trong `app.js` để lấy đầy đủ thông tin user từ `userService`
     - Đã thêm middleware `ensureUserWithRole` trong `commonService.js`
     - Đã cập nhật `homeController.js` để kiểm tra `role_id` trước khi sử dụng

3. **Token không hợp lệ**
   - Kiểm tra JWT_SECRET trong .env
   - Kiểm tra token có trong database không

4. **Single device login không hoạt động**
   - Kiểm tra trường `jwt_token_id` trong database
   - Kiểm tra middleware authentication

5. **Cookie không được set**
   - Kiểm tra cấu hình cookie (secure, httpOnly, sameSite)
   - Kiểm tra HTTPS trong production

### Debug:
- Sử dụng endpoint `/test-session` để kiểm tra trạng thái đăng nhập
- Chạy `node test-jwt.js` để test JWT service
- Chạy `node test-user-role.js` để test user role
- Kiểm tra logs trong console
- Kiểm tra database để xem token có được lưu không

## Migration từ Session

### Các thay đổi cần lưu ý:
1. Loại bỏ `req.session` và `req.sessionID`
2. Sử dụng `req.user` thay vì `req.isAuthenticated()`
3. Không cần `req.logIn()` và `req.logout()` của Passport
4. Token được lưu trong cookie thay vì session store
5. **Quan trọng:** Đã loại bỏ Passport Local Strategy, xác thực được thực hiện thủ công

### Backward Compatibility:
- Các middleware authentication vẫn giữ nguyên interface
- Response format không thay đổi
- Frontend không cần thay đổi (nếu sử dụng cookie)

## Cấu trúc file đã thay đổi:

### Files đã cập nhật:
- `app.js` - Thêm JWT middleware và passport config
- `controllers/userController.js` - Xác thực thủ công thay vì Passport Local
- `config/passport.js` - Chỉ giữ JWT Strategy
- `services/commonService.js` - Cập nhật middleware authentication
- `services/userService.js` - Loại bỏ session_id
- `package.json` - Xóa crypto dependency (built-in module)

### Files mới:
- `services/jwtService.js` - Service quản lý JWT
- `database/update_jwt_schema.sql` - Script cập nhật database
- `test-jwt.js` - File test JWT authentication
- `JWT_MIGRATION_GUIDE.md` - Hướng dẫn này 