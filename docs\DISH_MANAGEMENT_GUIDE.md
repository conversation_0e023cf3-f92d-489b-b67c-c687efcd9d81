# Hướng dẫn Hệ thống Quản lý Món ăn

## Tổng quan
Hệ thống quản lý món ăn cho phép tạo và quản lý các món ăn bao gồm nhiều thực phẩm khác nhau. Tổng dinh dưỡng của món ăn được tính toán động dựa trên khối lượng và thông tin dinh dưỡng của từng thực phẩm thành phần.

## Cấu trúc Database

### Bảng `dishes` (Món ăn)
```sql
- id: ID món ăn (tự tăng)
- name: Tên món ăn  
- description: Mô tả món ăn
- category: <PERSON><PERSON><PERSON> món ăn (ch<PERSON>h, phụ, tráng miệng, etc.)
- active: Tr<PERSON>ng thái (1: hoạt động, -1: đã xóa)
- created_by: ID người tạo
- created_at: Thời gian tạo
- updated_at: Thời gian cập nh<PERSON>t
```

### Bảng `dish_foods` (<PERSON><PERSON><PERSON><PERSON> phẩm trong món ăn)
```sql
- id: <PERSON> bản ghi (tự tăng)
- dish_id: ID món ăn (khóa ngoại)
- food_id: ID thực phẩm (khóa ngoại đến food_info)
- weight: Khối lượng thực phẩm (gram)
- order_index: Thứ tự hiển thị
```

## Tính toán Dinh dưỡng Động

### Nguyên tắc
- **KHÔNG lưu trữ** tổng số dinh dưỡng trong bảng `dishes`
- **Tính toán động** dựa trên:
  - Khối lượng thực phẩm trong `dish_foods.weight`
  - Thông tin dinh dưỡng từ `food_info` và `main_nutrients`
  - Công thức: `(giá_trị_dinh_dưỡng / 100) * khối_lượng`

### Ví dụ tính toán
```javascript
// Thực phẩm A: 100g có 200 kcal
// Trong món ăn: sử dụng 150g
// Năng lượng thực tế = (200 / 100) * 150 = 300 kcal
const ratio = weight / 100;
const actualEnergy = nutrientValue * ratio;
```

## API Endpoints

### Admin APIs
- `GET /admin/mon-an` - Danh sách món ăn
- `GET /admin/mon-an/new` - Form tạo món ăn mới
- `GET /admin/mon-an/:id` - Form sửa món ăn
- `POST /admin/mon-an/list` - DataTable data
- `POST /admin/mon-an/upsert` - Tạo/cập nhật món ăn
- `POST /admin/mon-an/delete/:id` - Xóa món ăn

### Public APIs
- `GET /admin/api/dishes-for-select` - Danh sách món ăn cho select
- `GET /admin/api/dish-foods/:id` - Chi tiết thực phẩm trong món ăn

## Tích hợp với Thực đơn

### Thêm món ăn vào thực đơn
1. Chọn món ăn từ dropdown
2. Chọn giờ ăn (sáng, trưa, chiều, tối)
3. Hệ thống tự động:
   - Lấy danh sách thực phẩm trong món ăn
   - Tính toán dinh dưỡng theo khối lượng thực tế
   - Thêm từng thực phẩm vào thực đơn
   - Cập nhật tổng cộng

### JavaScript Functions
```javascript
// Khởi tạo dropdown món ăn
generateDishName("dish_name");

// Thêm món ăn vào thực đơn  
addDishToMenu();

// Thêm thực phẩm từ món ăn
addDishFoodsToMenu(dishFoods, menuTimeId, dishName);
```

## Ưu điểm của Tính toán Động

### 1. Tính chính xác
- Luôn đảm bảo tổng số chính xác theo khối lượng thực tế
- Không có vấn đề đồng bộ dữ liệu

### 2. Linh hoạt
- Có thể thay đổi khối lượng thực phẩm mà không cần cập nhật tổng số
- Dễ dàng thêm/bớt thực phẩm trong món ăn

### 3. Hiệu suất
- Giảm dung lượng database (không lưu trữ dữ liệu tính toán)
- Tránh cập nhật không cần thiết

### 4. Bảo trì
- Ít lỗi logic do đồng bộ dữ liệu
- Dễ debug và kiểm tra

## Lưu ý Khi Phát triển

### 1. Backend
- Luôn tính toán tổng số trong query hoặc controller
- Không lưu trữ giá trị tính toán vào database
- Sử dụng JOIN để lấy thông tin dinh dưỡng

### 2. Frontend  
- Hiển thị tổng số tính toán từ server
- Cập nhật realtime khi thay đổi khối lượng
- Sử dụng `calculated_*` prefix cho giá trị đã tính

### 3. Performance
- Cache kết quả tính toán nếu cần
- Sử dụng index cho các trường JOIN
- Tối ưu query với EXPLAIN

## Ví dụ Sử dụng

### Tạo món ăn "Cơm gà"
```javascript
const dishData = {
    name: "Cơm gà",
    description: "Món cơm gà truyền thống",
    category: "chính",
    dish_foods: [
        { food_id: 1, weight: 200 }, // Cơm trắng 200g
        { food_id: 15, weight: 150 }, // Thịt gà 150g  
        { food_id: 30, weight: 50 }   // Rau xanh 50g
    ]
};
```

### Hiển thị trong DataTable
```javascript
// Tổng khối lượng và năng lượng được tính trong controller
{
    total_weight: 400, // 200 + 150 + 50
    total_energy: 650, // Tính theo công thức
    food_count: 3      // Số lượng thực phẩm
}
```

## Troubleshooting

### Lỗi thường gặp
1. **Tổng số không chính xác**: Kiểm tra công thức tính toán
2. **Performance chậm**: Tối ưu query JOIN
3. **Dữ liệu không đồng bộ**: Đảm bảo không cache cũ

### Debug
```sql
-- Kiểm tra tính toán tổng số
SELECT 
    d.name,
    SUM(df.weight) as total_weight,
    SUM((mn.energy * df.weight / 100)) as total_energy
FROM dishes d
LEFT JOIN dish_foods df ON d.id = df.dish_id  
LEFT JOIN main_nutrients mn ON df.food_id = mn.id_food
WHERE d.id = ?
GROUP BY d.id;
``` 