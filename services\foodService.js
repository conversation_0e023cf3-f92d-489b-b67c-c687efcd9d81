const commonService = require('./commonService');

const foodService = {
    // L<PERSON>y danh sách thực phẩm cho dropdown với filter và search
    getFoodForSelect: async (type = null, type_year = null, search = null) => {
        try {
            let whereClause = '';
            let params = [];
            let conditions = [];
            
            // Filter theo type
            if (type) {
                conditions.push('fi.type = ?');
                params.push(type);
            }
            
            // Filter theo type_year
            if (type_year) {
                conditions.push('fi.type_year = ?');
                params.push(type_year);
            }
            
            // Search theo tên thực phẩm
            if (search && search.trim().length > 0) {
                conditions.push('(fi.name LIKE ? OR fi.ten LIKE ? OR fi.code LIKE ?)');
                const searchTerm = `%${search.trim()}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }
            
            if (conditions.length > 0) {
                whereClause = 'WHERE ' + conditions.join(' AND ');
            }
            
            const sql = `
                SELECT 
                    -- <PERSON><PERSON><PERSON> bộ trường từ food_info
                    fi.id,
                    fi.code,
                    fi.name,
                    fi.type,
                    fi.type_year,
                    fi.ten,
                    fi.total_sugar,
                    fi.galactose,
                    fi.maltose,
                    fi.lactose,
                    fi.fructose,
                    fi.glucose,
                    fi.sucrose,
                    fi.lycopene,
                    fi.lutein_zeaxanthin,
                    fi.total_isoflavone,
                    fi.daidzein,
                    fi.genistein,
                    fi.glycetin,
                    fi.phytosterol,
                    fi.purine,
                    fi.weight,
                    fi.protein,
                    fi.in,
                    fi.lysin,
                    fi.methionin,
                    fi.tryptophan,
                    fi.phenylalanin,
                    fi.threonin,
                    fi.isoleucine,
                    fi.arginine,
                    fi.histidine,
                    fi.alanine,
                    fi.aspartic_acid,
                    fi.glutamic_acid,
                    fi.proline,
                    fi.serine,
                    fi.animal_protein,
                    fi.cystine,
                    fi.valine,
                    fi.tyrosine,
                    fi.leucine,
                    fi.lignoceric,
                    fi.unanimal_lipid,
                    fi.retinol,
                    fi.riboflavin,
                    fi.thiamine,
                    fi.niacin,
                    fi.pantothenic_acid,
                    fi.folate,
                    fi.folic_acid,
                    fi.biotin,
                    fi.caroten,
                    fi.vitamin_a_rae,
                    fi.vitamin_b1,
                    fi.vitamin_b2,
                    fi.vitamin_b6,
                    fi.vitamin_b12,
                    fi.vitamin_pp,
                    fi.vitamin_c,
                    fi.vitamin_e,
                    fi.vitamin_k,
                    fi.b_carotene,
                    fi.a_carotene,
                    fi.b_cryptoxanthin,
                    fi.created_at as fi_created_at,
                    fi.created_by,
                    
                    -- Toàn bộ trường từ main_nutrients
                    mn.id as mn_id,
                    mn.id_food,
                    mn.edible,
                    mn.energy,
                    mn.water,
                    mn.protein as mn_protein,
                    mn.fat,
                    mn.carbohydrate,
                    mn.fiber,
                    mn.ash,
                    mn.calci,
                    mn.phosphorous,
                    mn.fe,
                    mn.zinc,
                    mn.sodium,
                    mn.potassium,
                    mn.magnesium,
                    mn.manganese,
                    mn.copper,
                    mn.selenium,
                    mn.total_fat,
                    mn.total_saturated_fat,
                    mn.palmitic,
                    mn.margaric,
                    mn.stearic,
                    mn.arachidic,
                    mn.behenic,
                    mn.lignoceric as mn_lignoceric,
                    mn.mufa,
                    mn.myristoleic,
                    mn.palmitoleic,
                    mn.oleic,
                    mn.fufa,
                    mn.linoleic,
                    mn.linolenic,
                    mn.arachidonic,
                    mn.dha,
                    mn.trans_fatty_acids,
                    mn.cholesterol
                FROM food_info fi
                LEFT JOIN main_nutrients mn ON fi.id = mn.id_food
                ${whereClause}
                ORDER BY fi.name ASC
                LIMIT 100
            `;
            
            const result = await commonService.getListTable(sql, params);
            
            if (result.success && result.data) {
                return {
                    success: true,
                    data: result.data.map(food => ({
                        value: food.id,
                        label: `${food.name}${food.ten ? ' - ' + food.ten : ''} (${food.type === 'raw' ? 'Sống' : 'Chín'} - ${food.type_year})`,
                        customData: {
                            // Thông tin từ food_info
                            id: food.id,
                            code: food.code,
                            name: food.name,
                            type: food.type,
                            type_year: food.type_year,
                            ten: food.ten,
                            total_sugar: food.total_sugar,
                            galactose: food.galactose,
                            maltose: food.maltose,
                            lactose: food.lactose,
                            fructose: food.fructose,
                            glucose: food.glucose,
                            sucrose: food.sucrose,
                            lycopene: food.lycopene,
                            lutein_zeaxanthin: food.lutein_zeaxanthin,
                            total_isoflavone: food.total_isoflavone,
                            daidzein: food.daidzein,
                            genistein: food.genistein,
                            glycetin: food.glycetin,
                            phytosterol: food.phytosterol,
                            purine: food.purine,
                            weight: food.weight,
                            protein: food.protein,
                            in: food.in,
                            lysin: food.lysin,
                            methionin: food.methionin,
                            tryptophan: food.tryptophan,
                            phenylalanin: food.phenylalanin,
                            threonin: food.threonin,
                            isoleucine: food.isoleucine,
                            arginine: food.arginine,
                            histidine: food.histidine,
                            alanine: food.alanine,
                            aspartic_acid: food.aspartic_acid,
                            glutamic_acid: food.glutamic_acid,
                            proline: food.proline,
                            serine: food.serine,
                            animal_protein: food.animal_protein,
                            cystine: food.cystine,
                            valine: food.valine,
                            tyrosine: food.tyrosine,
                            leucine: food.leucine,
                            lignoceric: food.lignoceric,
                            unanimal_lipid: food.unanimal_lipid,
                            retinol: food.retinol,
                            riboflavin: food.riboflavin,
                            thiamine: food.thiamine,
                            niacin: food.niacin,
                            pantothenic_acid: food.pantothenic_acid,
                            folate: food.folate,
                            folic_acid: food.folic_acid,
                            biotin: food.biotin,
                            caroten: food.caroten,
                            vitamin_a_rae: food.vitamin_a_rae,
                            vitamin_b1: food.vitamin_b1,
                            vitamin_b2: food.vitamin_b2,
                            vitamin_b6: food.vitamin_b6,
                            vitamin_b12: food.vitamin_b12,
                            vitamin_pp: food.vitamin_pp,
                            vitamin_c: food.vitamin_c,
                            vitamin_e: food.vitamin_e,
                            vitamin_k: food.vitamin_k,
                            b_carotene: food.b_carotene,
                            a_carotene: food.a_carotene,
                            b_cryptoxanthin: food.b_cryptoxanthin,
                            created_at: food.fi_created_at,
                            created_by: food.created_by,
                            
                            // Thông tin từ main_nutrients
                            mn_id: food.mn_id,
                            id_food: food.id_food,
                            edible: food.edible,
                            energy: food.energy,
                            water: food.water,
                            mn_protein: food.mn_protein,
                            fat: food.fat,
                            carbohydrate: food.carbohydrate,
                            fiber: food.fiber,
                            ash: food.ash,
                            calci: food.calci,
                            phosphorous: food.phosphorous,
                            fe: food.fe,
                            zinc: food.zinc,
                            sodium: food.sodium,
                            potassium: food.potassium,
                            magnesium: food.magnesium,
                            manganese: food.manganese,
                            copper: food.copper,
                            selenium: food.selenium,
                            total_fat: food.total_fat,
                            total_saturated_fat: food.total_saturated_fat,
                            palmitic: food.palmitic,
                            margaric: food.margaric,
                            stearic: food.stearic,
                            arachidic: food.arachidic,
                            behenic: food.behenic,
                            mn_lignoceric: food.mn_lignoceric,
                            mufa: food.mufa,
                            myristoleic: food.myristoleic,
                            palmitoleic: food.palmitoleic,
                            oleic: food.oleic,
                            fufa: food.fufa,
                            linoleic: food.linoleic,
                            linolenic: food.linolenic,
                            arachidonic: food.arachidonic,
                            dha: food.dha,
                            trans_fatty_acids: food.trans_fatty_acids,
                            cholesterol: food.cholesterol
                        }
                    }))
                };
            } else {
                return {
                    success: false,
                    message: 'Không thể lấy danh sách thực phẩm',
                    data: []
                };
            }
        } catch (error) {
            console.error('Error in getFoodForSelect:', error);
            return {
                success: false,
                message: 'Có lỗi xảy ra khi lấy danh sách thực phẩm',
                data: []
            };
        }
    },
    
    // Lấy thông tin chi tiết thực phẩm
    getFoodDetail: async (id) => {
        try {
            const sql = `
                SELECT 
                    fi.*,
                    mn.*
                FROM food_info fi
                LEFT JOIN main_nutrients mn ON fi.id = mn.id_food
                WHERE fi.id = ?
            `;
            
            const result = await commonService.getListTable(sql, [id]);
            
            if (result.success && result.data && result.data.length > 0) {
                return {
                    success: true,
                    data: result.data[0]
                };
            } else {
                return {
                    success: false,
                    message: 'Không tìm thấy thực phẩm',
                    data: null
                };
            }
        } catch (error) {
            console.error('Error in getFoodDetail:', error);
            return {
                success: false,
                message: 'Có lỗi xảy ra khi lấy thông tin thực phẩm',
                data: null
            };
        }
    }
};

module.exports = foodService; 