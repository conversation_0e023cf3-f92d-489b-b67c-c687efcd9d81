<%- include('../layout/head', {title: isEdit ? 'S<PERSON><PERSON> thực phẩm' : 'Thêm thực phẩm mới'}) %>

<!-- Custom CSS for food form -->
<link href="/css/food-admin.css" rel="stylesheet">

<body id="page-top" class="food-form">
    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <%- include('../layout/sidebar', {active: 'thuc-pham'}) %>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <%- include('../layout/header') %>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <%= isEdit ? 'S<PERSON><PERSON> thực phẩm' : 'Thêm thực phẩm mới' %>
                        </h1>
                        <a href="/admin/thuc-pham" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Trở về danh sách
                        </a>
                    </div>

                    <!-- Form -->
                    <form id="foodForm">
                        <% if (isEdit && foodData) { %>
                            <input type="hidden" name="id" value="<%= foodData.id %>">
                        <% } %>

                        <!-- Thông tin cơ bản -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Thông tin cơ bản</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="code">Mã thực phẩm</label>
                                            <input type="text" class="form-control" id="code" name="code" 
                                                value="<%= foodData ? foodData.code || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="weight">Khối lượng (g) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="weight" name="weight" required
                                                value="<%= foodData ? foodData.weight || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">Tên thực phẩm (tiếng Anh) <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" required
                                                value="<%= foodData ? foodData.name || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="ten">Tên thực phẩm (tiếng Việt)</label>
                                            <input type="text" class="form-control" id="ten" name="ten"
                                                value="<%= foodData ? foodData.ten || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="type">Loại thực phẩm <span class="text-danger">*</span></label>
                                            <select class="form-control" id="type" name="type" required>
                                                <option value="raw" <%= foodData && foodData.type === 'raw' ? 'selected' : '' %>>Sống</option>
                                                <option value="cooked" <%= foodData && foodData.type === 'cooked' ? 'selected' : '' %>>Chín</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="type_year">Năm dữ liệu <span class="text-danger">*</span></label>
                                            <select class="form-control" id="type_year" name="type_year" required>
                                                <option value="2017" <%= foodData && foodData.type_year === '2017' ? 'selected' : '' %>>2017</option>
                                                <option value="2024" <%= foodData && foodData.type_year === '2024' ? 'selected' : '' %>>2024</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Main Nutrients -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 nutrients-basic">
                                <h6 class="m-0 font-weight-bold">Chất dinh dưỡng chính</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="edible">Phần ăn được (%)</label>
                                            <input type="number" class="form-control" id="edible" name="edible"
                                                value="<%= mainNutrients ? mainNutrients.edible || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="energy">Năng lượng (kcal)</label>
                                            <input type="number" class="form-control" id="energy" name="energy"
                                                value="<%= mainNutrients ? mainNutrients.energy || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="water">Nước (g)</label>
                                            <input type="text" class="form-control" id="water" name="water"
                                                value="<%= mainNutrients ? mainNutrients.water || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="nutrients_protein">Protein (g)</label>
                                            <input type="text" class="form-control" id="nutrients_protein" name="nutrients_protein"
                                                value="<%= mainNutrients ? mainNutrients.protein || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="fat">Lipid (g)</label>
                                            <input type="text" class="form-control" id="fat" name="fat"
                                                value="<%= mainNutrients ? mainNutrients.fat || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="carbohydrate">Carbohydrate (g)</label>
                                            <input type="text" class="form-control" id="carbohydrate" name="carbohydrate"
                                                value="<%= mainNutrients ? mainNutrients.carbohydrate || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="fiber">Chất xơ (g)</label>
                                            <input type="text" class="form-control" id="fiber" name="fiber"
                                                value="<%= mainNutrients ? mainNutrients.fiber || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="ash">Tro (g)</label>
                                            <input type="text" class="form-control" id="ash" name="ash"
                                                value="<%= mainNutrients ? mainNutrients.ash || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Khoáng chất -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 nutrients-minerals">
                                <h6 class="m-0 font-weight-bold">Khoáng chất</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="calci">Calci (mg)</label>
                                            <input type="number" class="form-control" id="calci" name="calci"
                                                value="<%= mainNutrients ? mainNutrients.calci || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="phosphorous">Phospho (mg)</label>
                                            <input type="text" class="form-control" id="phosphorous" name="phosphorous"
                                                value="<%= mainNutrients ? mainNutrients.phosphorous || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="fe">Sắt (mg)</label>
                                            <input type="text" class="form-control" id="fe" name="fe"
                                                value="<%= mainNutrients ? mainNutrients.fe || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="zinc">Kẽm (mg)</label>
                                            <input type="text" class="form-control" id="zinc" name="zinc"
                                                value="<%= mainNutrients ? mainNutrients.zinc || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="sodium">Natri (mg)</label>
                                            <input type="number" class="form-control" id="sodium" name="sodium"
                                                value="<%= mainNutrients ? mainNutrients.sodium || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="potassium">Kali (mg)</label>
                                            <input type="number" class="form-control" id="potassium" name="potassium"
                                                value="<%= mainNutrients ? mainNutrients.potassium || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="magnesium">Magie (mg)</label>
                                            <input type="number" class="form-control" id="magnesium" name="magnesium"
                                                value="<%= mainNutrients ? mainNutrients.magnesium || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="manganese">Mangan (mg)</label>
                                            <input type="text" class="form-control" id="manganese" name="manganese"
                                                value="<%= mainNutrients ? mainNutrients.manganese || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="copper">Đồng (mg)</label>
                                            <input type="number" class="form-control" id="copper" name="copper"
                                                value="<%= mainNutrients ? mainNutrients.copper || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="selenium">Selen (μg)</label>
                                            <input type="text" class="form-control" id="selenium" name="selenium"
                                                value="<%= mainNutrients ? mainNutrients.selenium || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Axit béo -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 nutrients-fatty">
                                <h6 class="m-0 font-weight-bold">Axit béo</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="total_fat">Tổng lipid (g)</label>
                                            <input type="text" class="form-control" id="total_fat" name="total_fat"
                                                value="<%= mainNutrients ? mainNutrients.total_fat || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="total_saturated_fat">Axit béo bão hòa (g)</label>
                                            <input type="text" class="form-control" id="total_saturated_fat" name="total_saturated_fat"
                                                value="<%= mainNutrients ? mainNutrients.total_saturated_fat || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="mufa">MUFA (g)</label>
                                            <input type="text" class="form-control" id="mufa" name="mufa"
                                                value="<%= mainNutrients ? mainNutrients.mufa || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="fufa">PUFA (g)</label>
                                            <input type="text" class="form-control" id="fufa" name="fufa"
                                                value="<%= mainNutrients ? mainNutrients.fufa || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="palmitic">Palmitic (g)</label>
                                            <input type="text" class="form-control" id="palmitic" name="palmitic"
                                                value="<%= mainNutrients ? mainNutrients.palmitic || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="stearic">Stearic (g)</label>
                                            <input type="text" class="form-control" id="stearic" name="stearic"
                                                value="<%= mainNutrients ? mainNutrients.stearic || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="oleic">Oleic (g)</label>
                                            <input type="text" class="form-control" id="oleic" name="oleic"
                                                value="<%= mainNutrients ? mainNutrients.oleic || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="linoleic">Linoleic (g)</label>
                                            <input type="text" class="form-control" id="linoleic" name="linoleic"
                                                value="<%= mainNutrients ? mainNutrients.linoleic || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="trans_fatty_acids">Trans fatty acids (g)</label>
                                            <input type="text" class="form-control" id="trans_fatty_acids" name="trans_fatty_acids"
                                                value="<%= mainNutrients ? mainNutrients.trans_fatty_acids || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="cholesterol">Cholesterol (mg)</label>
                                            <input type="text" class="form-control" id="cholesterol" name="cholesterol"
                                                value="<%= mainNutrients ? mainNutrients.cholesterol || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Protein và amino acid -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 nutrients-protein">
                                <h6 class="m-0 font-weight-bold">Protein và Amino acid</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="protein">Protein (g)</label>
                                            <input type="text" class="form-control" id="protein" name="protein"
                                                value="<%= foodData ? foodData.protein || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="animal_protein">Animal protein (g)</label>
                                            <input type="text" class="form-control" id="animal_protein" name="animal_protein"
                                                value="<%= foodData ? foodData.animal_protein || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="lysin">Lysin (mg)</label>
                                            <input type="text" class="form-control" id="lysin" name="lysin"
                                                value="<%= foodData ? foodData.lysin || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="methionin">Methionin (mg)</label>
                                            <input type="text" class="form-control" id="methionin" name="methionin"
                                                value="<%= foodData ? foodData.methionin || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="tryptophan">Tryptophan (mg)</label>
                                            <input type="text" class="form-control" id="tryptophan" name="tryptophan"
                                                value="<%= foodData ? foodData.tryptophan || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="phenylalanin">Phenylalanin (mg)</label>
                                            <input type="text" class="form-control" id="phenylalanin" name="phenylalanin"
                                                value="<%= foodData ? foodData.phenylalanin || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="threonin">Threonin (mg)</label>
                                            <input type="text" class="form-control" id="threonin" name="threonin"
                                                value="<%= foodData ? foodData.threonin || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isoleucine">Isoleucine (mg)</label>
                                            <input type="text" class="form-control" id="isoleucine" name="isoleucine"
                                                value="<%= foodData ? foodData.isoleucine || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="arginine">Arginine (mg)</label>
                                            <input type="text" class="form-control" id="arginine" name="arginine"
                                                value="<%= foodData ? foodData.arginine || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="histidine">Histidine (mg)</label>
                                            <input type="text" class="form-control" id="histidine" name="histidine"
                                                value="<%= foodData ? foodData.histidine || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="valine">Valine (mg)</label>
                                            <input type="text" class="form-control" id="valine" name="valine"
                                                value="<%= foodData ? foodData.valine || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="leucine">Leucine (mg)</label>
                                            <input type="text" class="form-control" id="leucine" name="leucine"
                                                value="<%= foodData ? foodData.leucine || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Vitamin -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 nutrients-vitamins">
                                <h6 class="m-0 font-weight-bold">Vitamin</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_a_rae">Vitamin A (μg RAE)</label>
                                            <input type="text" class="form-control" id="vitamin_a_rae" name="vitamin_a_rae"
                                                value="<%= foodData ? foodData.vitamin_a_rae || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_b1">Vitamin B1 (mg)</label>
                                            <input type="text" class="form-control" id="vitamin_b1" name="vitamin_b1"
                                                value="<%= foodData ? foodData.vitamin_b1 || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_b2">Vitamin B2 (mg)</label>
                                            <input type="text" class="form-control" id="vitamin_b2" name="vitamin_b2"
                                                value="<%= foodData ? foodData.vitamin_b2 || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_b6">Vitamin B6 (mg)</label>
                                            <input type="text" class="form-control" id="vitamin_b6" name="vitamin_b6"
                                                value="<%= foodData ? foodData.vitamin_b6 || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_b12">Vitamin B12 (μg)</label>
                                            <input type="text" class="form-control" id="vitamin_b12" name="vitamin_b12"
                                                value="<%= foodData ? foodData.vitamin_b12 || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_c">Vitamin C (mg)</label>
                                            <input type="text" class="form-control" id="vitamin_c" name="vitamin_c"
                                                value="<%= foodData ? foodData.vitamin_c || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_e">Vitamin E (mg)</label>
                                            <input type="text" class="form-control" id="vitamin_e" name="vitamin_e"
                                                value="<%= foodData ? foodData.vitamin_e || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="vitamin_k">Vitamin K (μg)</label>
                                            <input type="text" class="form-control" id="vitamin_k" name="vitamin_k"
                                                value="<%= foodData ? foodData.vitamin_k || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="folate">Folate (μg)</label>
                                            <input type="number" class="form-control" id="folate" name="folate"
                                                value="<%= foodData ? foodData.folate || '' : '' %>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="niacin">Niacin (mg)</label>
                                            <input type="text" class="form-control" id="niacin" name="niacin"
                                                value="<%= foodData ? foodData.niacin || '' : '' %>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Nút lưu -->
                        <div class="card shadow mb-4">
                            <div class="card-body text-center">
                                <button type="submit" class="btn btn-primary mr-2">
                                    <i class="fas fa-save"></i> 
                                    <%= isEdit ? 'Cập nhật' : 'Lưu' %>
                                </button>
                                <a href="/admin/thuc-pham" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Hủy
                                </a>
                            </div>
                        </div>
                    </form>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <%- include('../layout/footer') %>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript-->
    <script src="/vendor/jquery/jquery.min.js"></script>
    <script src="/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="/js/sb-admin-2.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="/js/<EMAIL>"></script>

    <script>
        $(document).ready(function() {
            $('#foodForm').on('submit', function(e) {
                e.preventDefault();
                
                // Disable submit button
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
                
                $.ajax({
                    url: '/admin/thuc-pham/upsert/',
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Thành công!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    if (response.data && response.data.id) {
                                        // Redirect to edit page if it's a new record
                                        window.location.href = `/admin/thuc-pham/${response.data.id}`;
                                    } else {
                                        // Reload current page if it's an edit
                                        window.location.reload();
                                    }
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Lỗi!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Lỗi!',
                            text: 'Có lỗi xảy ra khi lưu dữ liệu.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    },
                    complete: function() {
                        // Re-enable submit button
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });
    </script>

</body>
</html> 