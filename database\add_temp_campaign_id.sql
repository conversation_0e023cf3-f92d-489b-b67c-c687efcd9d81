-- Add temp_campaign_id column to user_sessions table
-- This allows admin users to temporarily switch campaigns without changing their original campaign_id

ALTER TABLE user_sessions 
ADD COLUMN temp_campaign_id INT NULL 
COMMENT 'Temporary campaign ID for admin users to switch campaigns' 
AFTER user_agent;

-- Add index for better performance when querying by temp_campaign_id
CREATE INDEX idx_user_sessions_temp_campaign_id ON user_sessions(temp_campaign_id);

-- Add foreign key constraint to ensure temp_campaign_id references valid campaign
ALTER TABLE user_sessions 
ADD CONSTRAINT fk_user_sessions_temp_campaign 
FOREIGN KEY (temp_campaign_id) REFERENCES campaign(id) 
ON DELETE SET NULL ON UPDATE CASCADE;
