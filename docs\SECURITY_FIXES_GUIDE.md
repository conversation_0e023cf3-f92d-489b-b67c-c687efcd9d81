# Hướng Dẫn Sử Dụng Security Fixes

## Tổng <PERSON>uan

<PERSON> implement các security fixes quan trọng để ngăn chặn SQL Injection và cải thiện Authorization. Các thay đổi ch<PERSON>h bao gồm:

## 1. Security Service (services/securityService.js)

### Validation Input
```javascript
const schema = {
    email: {
        required: true,
        type: 'email',
        message: '<PERSON><PERSON> không hợp lệ'
    },
    password: {
        required: true,
        type: 'string',
        minLength: 8,
        message: '<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự'
    }
};

const validation = securityService.validateInput(req.body, schema);
if (!validation.isValid) {
    return res.json({
        success: false,
        message: validation.errors.map(e => e.message).join(', ')
    });
}
```

### Authorization
```javascript
// Kiểm tra quyền trong controller
const authResult = securityService.checkAuthorization(req.user, 'viem-gan', 'read');
if (!authResult.authorized) {
    return res.status(403).json({
        success: false,
        message: authResult.errors.join(', ')
    });
}

// Hoặc sử dụng middleware
router.get("/viem-gan", 
    commonService.isAuthenticated, 
    securityService.requirePermission('viem-gan', 'read'), 
    controller.action
);
```

## 2. SQL Injection Prevention

### CommonService Improvements
- Validate tất cả database identifiers (table names, column names)
- Sử dụng parameterized queries
- Whitelist allowed operators

```javascript
// Trước (unsafe):
let sql = `SELECT * FROM ${table} WHERE name = '${userInput}'`;

// Sau (safe):
securityService.validateDbIdentifier(table);
let sql = `SELECT * FROM ${table} WHERE name = ?`;
let params = [userInput];
```

## 3. Role-Based Access Control

### Role Permissions
```javascript
const rolePermissions = {
    1: { // Admin
        all: ['*'],
        read: [],
        write: [],
        delete: []
    },
    3: { // Viêm gan
        read: ['viem-gan', 'patient'],
        write: ['viem-gan', 'patient'],
        delete: ['viem-gan'],
        all: []
    },
    4: { // Uốn ván
        read: ['uon-van', 'patient'],
        write: ['uon-van', 'patient'],
        delete: ['uon-van'],
        all: []
    },
    5: { // Hội chẩn
        read: ['hoi-chan', 'patient'],
        write: ['hoi-chan', 'patient'],
        delete: ['hoi-chan'],
        all: []
    },
    6: { // Viêm gan MT1
        read: ['viem-gan-mt1', 'patient', 'khau-phan-an'],
        write: ['viem-gan-mt1', 'patient', 'khau-phan-an'],
        delete: ['viem-gan-mt1'],
        all: []
    },
    7: { // Research
        read: ['research', 'patient'],
        write: ['research', 'patient'],
        delete: ['research'],
        all: []
    },
    8: { // Standard
        read: ['standard', 'patient'],
        write: ['standard', 'patient'],
        delete: ['standard'],
        all: []
    }
};
```

### Data Filtering Based on User Role
```javascript
// Trong controller - filter data theo role
function getFilteredData(req, tableName, additionalConditions = {}) {
    let conditions = { ...additionalConditions };

    // Admin có thể xem tất cả
    if (!req.user.isAdmin) {
        // Regular users chỉ xem records của họ
        conditions.created_by = req.user.id;
    }

    return commonService.getAllDataTable(tableName, conditions);
}

// Ví dụ sử dụng trong controller
async function getPatientList(req, res) {
    try {
        const authResult = securityService.checkAuthorization(req.user, 'patient', 'read');
        if (!authResult.authorized) {
            return res.status(403).json({
                success: false,
                message: 'Bạn không có quyền truy cập danh sách này!'
            });
        }

        // Filter data theo role
        const patients = await getFilteredData(req, 'patients', { status: 'active' });

        res.json({
            success: true,
            data: patients
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Lỗi server'
        });
    }
}
```

## 4. Audit Service (services/auditService.js)

### Logging Activities
```javascript
// Log authentication events
await auditService.logAuthEvent(email, 'LOGIN', true, {
    userId: user.id,
    deviceInfo: deviceInfo.deviceName
}, req.ip);

// Log user activities
await auditService.logActivity(
    req.user.id,
    'CREATE',
    'patient',
    { patientId: newPatient.id },
    req.ip
);
```

### Audit Middleware
```javascript
router.post("/patient/create",
    commonService.isAuthenticatedPost,
    auditService.createAuditMiddleware('CREATE', 'patient'),
    patient.create
);
```

### Complete Security Middleware Examples

#### GET Routes với Permission Check và Audit
```javascript
// Viêm gan routes
router.get('/viem-gan/:patient_id/:type',
    commonService.isAuthenticated,
    securityService.requirePermission('viem-gan', 'read'),
    auditService.createAuditMiddleware('VIEW', 'viem-gan'),
    hepatitis.index
);

// Uốn ván routes
router.get('/uon-van/:patient_id/:type',
    commonService.isAuthenticated,
    securityService.requirePermission('uon-van', 'read'),
    auditService.createAuditMiddleware('VIEW', 'uon-van'),
    tetanus.index
);

// Hội chẩn routes
router.get('/hoi-chan/:patient_id/:type',
    commonService.isAuthenticated,
    securityService.requirePermission('hoi-chan', 'read'),
    auditService.createAuditMiddleware('VIEW', 'hoi-chan'),
    liverSurgery.index
);

// Research routes
router.get('/research',
    commonService.isAuthenticated,
    securityService.requirePermission('research', 'read'),
    auditService.createAuditMiddleware('VIEW', 'research'),
    research.getlist
);

// Standard routes
router.get('/standard/:patient_id/:type',
    commonService.isAuthenticated,
    securityService.requirePermission('standard', 'read'),
    auditService.createAuditMiddleware('VIEW', 'standard'),
    standard.index
);

// Viêm gan MT1 routes
router.get('/viem-gan-mt1/:patient_id/:type',
    commonService.isAuthenticated,
    securityService.requirePermission('viem-gan-mt1', 'read'),
    auditService.createAuditMiddleware('VIEW', 'viem-gan-mt1'),
    hepatitisMt1.index
);
```

#### POST Routes với Permission Check và Audit
```javascript
// CREATE operations
router.post("/viem-gan-create/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('viem-gan', 'write'),
    auditService.createAuditMiddleware('CREATE', 'viem-gan'),
    hepatitis.createHepatitis
);

router.post("/uon-van-create/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('uon-van', 'write'),
    auditService.createAuditMiddleware('CREATE', 'uon-van'),
    tetanus.createTetanus
);

router.post("/research/create",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('research', 'write'),
    auditService.createAuditMiddleware('CREATE', 'research'),
    research.add
);

// UPDATE operations
router.post("/viem-gan-update/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('viem-gan', 'write'),
    auditService.createAuditMiddleware('UPDATE', 'viem-gan'),
    hepatitis.editHepatitis
);

router.post("/uon-van-update/:patient_id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('uon-van', 'write'),
    auditService.createAuditMiddleware('UPDATE', 'uon-van'),
    tetanus.editTetanus
);

// DELETE operations
router.post("/viem-gan/delete/time/:id/:type",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('viem-gan', 'delete'),
    auditService.createAuditMiddleware('DELETE', 'viem-gan'),
    hepatitis.deleteTime
);

router.post("/research/delete/:id",
    commonService.isAuthenticatedPost,
    securityService.requirePermission('research', 'delete'),
    auditService.createAuditMiddleware('DELETE', 'research'),
    research.delete
);

// LIST operations (DataTables)
router.post("/viem-gan-list/:patient_id/:type",
    commonService.isAuthenticatedPostList,
    securityService.requirePermission('viem-gan', 'read'),
    auditService.createAuditMiddleware('LIST', 'viem-gan'),
    hepatitis.getListTable
);

router.post("/research/list",
    commonService.isAuthenticatedPostList,
    securityService.requirePermission('research', 'read'),
    auditService.createAuditMiddleware('LIST', 'research'),
    research.getListTable
);
```

## 5. Standardized Response Format

### Error Response
```javascript
const errorResponse = securityService.createErrorResponse(
    'Validation failed',
    validationErrors,
    400
);
return res.status(400).json(errorResponse);
```

### Success Response
```javascript
const successResponse = securityService.createSuccessResponse(
    userData,
    'User created successfully'
);
return res.json(successResponse);
```

## 6. Removed Security Risks

### Console.log Removal
- Removed all console.log statements that could leak sensitive information
- Replaced with proper audit logging

### Input Sanitization
- All user inputs are now validated and sanitized
- XSS prevention in string inputs
- SQL injection prevention in database queries

## 7. Database Schema Requirements

Cần tạo bảng audit logs:

```sql
-- Audit logs table
CREATE TABLE audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(50),
    resource VARCHAR(50),
    details JSON,
    ip_address VARCHAR(45),
    timestamp DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Auth logs table
CREATE TABLE auth_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255),
    action VARCHAR(50),
    success TINYINT(1),
    details JSON,
    ip_address VARCHAR(45),
    timestamp DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 8. Best Practices Implemented

1. **Input Validation**: Tất cả inputs được validate trước khi xử lý
2. **Output Encoding**: Responses được standardize
3. **Authorization**: Kiểm tra quyền ở mọi endpoint
4. **SQL Injection Prevention**: Parameterized queries và input validation
5. **Audit Logging**: Track tất cả activities quan trọng
6. **Error Handling**: Consistent error responses
7. **Security Headers**: Implement qua middleware

## 9. Testing Security Fixes

### Test SQL Injection
```bash
# Test với malicious input
curl -X POST http://localhost:3000/api/patients \
  -H "Content-Type: application/json" \
  -d '{"name": "test'; DROP TABLE users; --"}'

# Expected: Validation error, không execute malicious SQL
```

### Test Authorization
```bash
# Test access without proper role
curl -X GET http://localhost:3000/viem-gan \
  -H "Authorization: Bearer [token_without_viem_gan_role]"

# Expected: 403 Forbidden
```

## 10. Monitoring và Alerts

- Monitor audit_logs table cho suspicious activities
- Set up alerts cho failed login attempts
- Track unusual data access patterns
- Monitor database query performance

## Kết Luận

Các security fixes này đã giải quyết:
- ✅ SQL Injection vulnerabilities
- ✅ Authorization bypass issues  
- ✅ Information disclosure via console.log
- ✅ Inconsistent error handling
- ✅ Missing input validation
- ✅ Lack of activity auditing

System giờ đây an toàn hơn đáng kể và có audit trail đầy đủ cho compliance. 