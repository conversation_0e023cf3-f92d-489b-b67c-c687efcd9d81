/**
 * Security Implementation Test
 * Test các security fixes đ<PERSON> được áp dụng
 */

const request = require('supertest');
const app = require('../app');
const securityService = require('../services/securityService');
const auditService = require('../services/auditService');

describe('Security Implementation Tests', () => {
    
    describe('Role-based Access Control', () => {
        
        test('Admin user should access all resources', () => {
            const adminUser = {
                id: 1,
                isAdmin: true,
                role_id: [1]
            };
            
            const authResult = securityService.checkAuthorization(adminUser, 'viem-gan', 'read');
            expect(authResult.authorized).toBe(true);
            
            const authResult2 = securityService.checkAuthorization(adminUser, 'research', 'delete');
            expect(authResult2.authorized).toBe(true);
        });
        
        test('Regular user should only access permitted resources', () => {
            const regularUser = {
                id: 2,
                isAdmin: false,
                role_id: [3] // Viêm gan role
            };
            
            // Should have access to viem-gan
            const authResult1 = securityService.checkAuthorization(regularUser, 'viem-gan', 'read');
            expect(authResult1.authorized).toBe(true);
            
            // Should NOT have access to research
            const authResult2 = securityService.checkAuthorization(regularUser, 'research', 'read');
            expect(authResult2.authorized).toBe(false);
        });
        
        test('User without role should be denied access', () => {
            const noRoleUser = {
                id: 3,
                isAdmin: false,
                role_id: []
            };
            
            const authResult = securityService.checkAuthorization(noRoleUser, 'viem-gan', 'read');
            expect(authResult.authorized).toBe(false);
            expect(authResult.errors).toContain('Ban khong co quyen truy cap tinh nang nay');
        });
    });
    
    describe('Role-based Data Filtering', () => {
        
        test('Admin should see all records', () => {
            const adminUser = {
                id: 1,
                isAdmin: true,
                role_id: [1]
            };
            
            const baseConditions = { patient_id: 123, active: 1 };
            const filteredConditions = securityService.applyRoleBasedFiltering(adminUser, baseConditions);
            
            // Admin should not have created_by filter
            expect(filteredConditions.created_by).toBeUndefined();
            expect(filteredConditions.patient_id).toBe(123);
            expect(filteredConditions.active).toBe(1);
        });
        
        test('Regular user should only see their records', () => {
            const regularUser = {
                id: 2,
                isAdmin: false,
                role_id: [3]
            };
            
            const baseConditions = { patient_id: 123, active: 1 };
            const filteredConditions = securityService.applyRoleBasedFiltering(regularUser, baseConditions);
            
            // Regular user should have created_by filter
            expect(filteredConditions.created_by).toBe(2);
            expect(filteredConditions.patient_id).toBe(123);
            expect(filteredConditions.active).toBe(1);
        });
    });
    
    describe('Input Validation', () => {
        
        test('Should validate required fields', () => {
            const inputData = {
                name: '',
                email: '<EMAIL>'
            };
            
            const schema = {
                name: { required: true, type: 'string', message: 'Name is required' },
                email: { required: true, type: 'email', message: 'Valid email is required' }
            };
            
            const result = securityService.validateInput(inputData, schema);
            expect(result.isValid).toBe(false);
            expect(result.errors.some(e => e.message === 'Name is required')).toBe(true);
        });
        
        test('Should sanitize input data', () => {
            const inputData = {
                name: '<script>alert("xss")</script>John',
                description: 'javascript:void(0)'
            };
            
            const schema = {
                name: { required: true, type: 'string' },
                description: { required: false, type: 'string' }
            };
            
            const result = securityService.validateInput(inputData, schema);
            expect(result.data.name).not.toContain('<script>');
            expect(result.data.description).not.toContain('javascript:');
        });
    });
    
    describe('Database Identifier Validation', () => {
        
        test('Should allow valid identifiers', () => {
            expect(() => {
                securityService.validateDbIdentifier('patients');
            }).not.toThrow();
            
            expect(() => {
                securityService.validateDbIdentifier('viem_gan_ls');
            }).not.toThrow();
        });
        
        test('Should reject dangerous identifiers', () => {
            expect(() => {
                securityService.validateDbIdentifier('patients; DROP TABLE users;');
            }).toThrow('Database identifier contains dangerous patterns');
            
            expect(() => {
                securityService.validateDbIdentifier('patients UNION SELECT');
            }).toThrow('Database identifier contains dangerous patterns');
        });
    });
    
    describe('Audit Service', () => {
        
        test('Should sanitize action strings', () => {
            expect(auditService.sanitizeAction('CREATE')).toBe('CREATE');
            expect(auditService.sanitizeAction('invalid_action')).toBe('OTHER');
            expect(auditService.sanitizeAction(null)).toBe('UNKNOWN');
        });
        
        test('Should sanitize resource strings', () => {
            expect(auditService.sanitizeResource('viem-gan')).toBe('viem-gan');
            expect(auditService.sanitizeResource('viem<script>gan')).toBe('viemscriptgan');
            expect(auditService.sanitizeResource(null)).toBe('UNKNOWN');
        });
        
        test('Should create audit middleware', () => {
            const middleware = auditService.createAuditMiddleware('CREATE', 'patient');
            expect(typeof middleware).toBe('function');
        });
    });
    
    describe('Response Formatting', () => {
        
        test('Should create error response', () => {
            const errorResponse = securityService.createErrorResponse(
                'Validation failed',
                ['Field is required'],
                400
            );
            
            expect(errorResponse.success).toBe(false);
            expect(errorResponse.message).toBe('Validation failed');
            expect(errorResponse.error).toEqual(['Field is required']);
            expect(errorResponse.statusCode).toBe(400);
        });
        
        test('Should create success response', () => {
            const successResponse = securityService.createSuccessResponse(
                { id: 1, name: 'Test' },
                'Created successfully'
            );
            
            expect(successResponse.success).toBe(true);
            expect(successResponse.message).toBe('Created successfully');
            expect(successResponse.data).toEqual({ id: 1, name: 'Test' });
            expect(successResponse.error).toBe(null);
        });
    });
});

// Test helper functions
function createMockRequest(user, body = {}, params = {}) {
    return {
        user: user,
        body: body,
        params: params,
        ip: '127.0.0.1',
        get: (header) => header === 'User-Agent' ? 'Test Agent' : null
    };
}

function createMockResponse() {
    const res = {
        statusCode: 200,
        json: jest.fn(),
        status: jest.fn().mockReturnThis(),
        render: jest.fn(),
        on: jest.fn()
    };
    return res;
}

module.exports = {
    createMockRequest,
    createMockResponse
};
